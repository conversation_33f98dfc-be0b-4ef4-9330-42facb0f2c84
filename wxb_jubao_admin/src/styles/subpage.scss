.filter-container {
  font-size: 0
}

.filter-container > div {
  margin-right: 10px
}

.filter-container button {
  margin-left: 10px;
  margin-right: 10px
}

.filter-container button span {
  color: #fff;
  font-size: 15px;
}

.filter-container span {
  font-size: 18px;
  color: #555;
  font-weight: bold;
  margin-right: 10px;
}

.filter-container em {
  font-size: 13px;
  color: #555;
  margin-right: 10px;
  margin-left: 5px;
  font-style: normal;
}

.dialogwidth {
  width: 80%
}

.lb {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  justify-content: space-between;
}

.lb .el-form-item {
  display: inline-block;
  width: 500px;
  margin-left: 50px;
  margin-right: 50px;
}

.lb .el-input--medium .el-input__inner {
  max-width: 400px;
  display: inline-block
}

.sb {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  justify-content: space-between;
}

.sb .el-form-item {
  display: inline-block;
  margin-left: 50px;
  margin-right: 50px;
  width: 100%;
}

.sb .el-input--medium .el-input__inner {
  max-width: 400px;
  display: inline-block
}

.sublist {
  display: flex;
  align-items: center
}

.el-pagination {
  text-align: center;
}

.my-blue {
  background-color: #4472C4;
  color: white;
}

.my-grey {
  color: white;
  background: gray;
}

.my-green {
  background-color: #5dd39e;
  color: white;
}

.my-red {
  background-color: #f25f5c;
  color: white;
}

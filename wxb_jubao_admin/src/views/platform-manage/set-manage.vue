<template>
  <div class="app-container">
    <el-form
      ref="dataForm"
      label-position="right"
      label-width="30%">
      <div>
        <el-divider content-position="left" v-if="temporary_park_list.length>0"><i class="el-icon-setting"></i> 临时停车配置</el-divider>
        <el-form-item :label="li.name" v-for="(li,index) in temporary_park_list" :key="li.id" v-if="li.config_class==='temporary_park'">
          <el-row style="display: flex;flex-direction: row;justify-content: left">
            <el-input style="width: 30%;" v-model="li.config_value" @input="li.config_value=li.config_value.replace(/[^\d]/g,'')"></el-input>
            <el-button style="margin-left: 15px;width: 100px" :loading="li.loading" icon="el-icon-refresh" type="info" @click="getDetailSet('temporary_park')" v-if="index === temporary_park_list.length - 1">重置</el-button>
            <el-button style="margin-left: 15px;width: 100px" :loading="li.loading" icon="el-icon-check" size="medium" type="primary" @click="editTimeSet('temporary_park')" v-if="index === temporary_park_list.length - 1">保存</el-button>
          </el-row>
        </el-form-item>
        <el-divider content-position="left" v-if="business_list.length>0"><i class="el-icon-setting"></i> 商家配置</el-divider>
        <el-form-item :label="li.name" v-for="(li,index) in business_list" :key="li.id" v-if="li.config_class==='business'">
          <el-row style="display: flex;flex-direction: row;justify-content: left">
            <el-input style="width: 30%;" v-model="li.config_value" @input="li.config_value=li.config_value.replace(/[^\d]/g,'')"></el-input>
            <el-button style="margin-left: 15px;width: 100px" :loading="li.loading" icon="el-icon-refresh" type="info" @click="getDetailSet('business')" v-if="index === business_list.length - 1">重置</el-button>
            <el-button style="margin-left: 15px;width: 100px" :loading="li.loading" icon="el-icon-check" size="medium" type="primary" @click="editTimeSet('business')" v-if="index === business_list.length - 1">保存</el-button>
          </el-row>
        </el-form-item>
        <el-divider content-position="left" v-if="lot_list.length>0"><i class="el-icon-setting"></i> 停车场配置</el-divider>
        <el-form-item :label="li.name" v-for="(li,index) in lot_list" :key="li.id" v-if="li.config_class==='lot'">
          <el-row style="display: flex;flex-direction: row;justify-content: left">
            <el-input style="width: 30%;" v-model="li.config_value" @input="li.config_value=li.config_value.replace(/[^\d]/g,'')"></el-input>
            <el-button style="margin-left: 15px;width: 100px" :loading="li.loading" icon="el-icon-refresh" type="info" @click="getDetailSet('lot')" v-if="index === lot_list.length - 1">重置</el-button>
            <el-button style="margin-left: 15px;width: 100px" :loading="li.loading" icon="el-icon-check" size="medium" type="primary" @click="editTimeSet('lot')" v-if="index === lot_list.length - 1">保存</el-button>
          </el-row>
        </el-form-item>
        <el-divider content-position="left" v-if="user_monthly_list.length>0"><i class="el-icon-setting"></i> 用户配置</el-divider>
        <el-form-item :label="li.name" v-for="(li,index) in user_monthly_list" :key="li.id" v-if="li.config_class==='user_monthly'">
          <el-row style="display: flex;flex-direction: row;justify-content: left">
            <el-input style="width: 30%;" v-model="li.config_value" @input="li.config_value=li.config_value.replace(/[^\d]/g,'')"></el-input>
            <el-button style="margin-left: 15px;width: 100px" :loading="li.loading" icon="el-icon-refresh" type="info" @click="getDetailSet('user_monthly')" v-if="index === user_monthly_list.length - 1">重置</el-button>
            <el-button style="margin-left: 15px;width: 100px" :loading="li.loading" icon="el-icon-check" size="medium" type="primary" @click="editTimeSet('user_monthly')" v-if="index === user_monthly_list.length - 1">保存</el-button>
          </el-row>
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>

<script>
  import Pagination from '@/components/Pagination/index' // secondary package based on el-pagination
  import Vue from 'vue'
  import { getSysConfig, editSysConfig, getSysConfigDetail } from '@/api/system'

  export default {
    components: { Pagination },
    data() {
      return {
        list: [],
        total: 0,
        timeList: [],
        listLoading: false,
        form: {},
        dialogTimeFormVisible: false,
        dialogFormVisible: false,
        zero: '0',
        temporary_park_list: [],
        business_list: [],
        lot_list: [],
        user_monthly_list: [],
        textMap: {
          edit: '编辑套餐',
          create: '添加套餐'
        },
        timeSetList: [30, 60, 10],
        dialogStatus: '',
        listQuery: {
          page: 1,
          limit: 99,
          start_time: null,
          end_time: null
        }
      }
    },
    created() {
      this.getList()
    },
    mounted() {
    },
    methods: {
      //获取收费标准数据
      getList() {
        let _this = this
        _this.listLoading = true
        _this.temporary_park_list = []
        _this.business_list = []
        _this.lot_list = []
        _this.user_monthly_list = []
        _this.timeList = [{ name: '临时停车', charge1: '0' }]
        // getSysConfig({ filter: { config_class: 'temporary_park' } }).then(response => {
        getSysConfig().then(response => {
          response.items.forEach(ele => {
            ele.loading = false
            switch (ele.config_class) {
              case 'lot':
                _this.lot_list.push(ele)
                break;
              case 'business':
                _this.business_list.push(ele)
                break;
              case 'temporary_park':
                _this.temporary_park_list.push(ele)
                break;
              case 'user_monthly':
                _this.user_monthly_list.push(ele)
                break;
            }
            _this.timeTablePerform(ele)
          })
          _this.total = response.totalCount
          _this.listLoading = false
        })
        setTimeout(() => {
          _this.listLoading = false
        }, 0.5 * 1000)
      },
      //获取单条数据
      getDetailSet(config_class) {
        let _this = this
        getSysConfig({ filter: { config_class: config_class } }).then(response => {
            if (response.code === 20000) {
              Vue.set(_this, config_class + '_list', response.items)
            }
          })
        setTimeout(() => {
          _this.listLoading = false
        }, 0.5 * 1000)
      },
      //格式化数据
      timeTablePerform(ele) {
        switch (ele.config_key) {
          case 'free_time':
            Vue.set(this.timeSetList, 0, ele.config_value)
            break
          case 'time_unit':
            Vue.set(this.timeSetList, 1, ele.config_value)
            break
          case 'first_fee':
            Vue.set(this.timeList[0], 'charge2', ele.config_value)
            break
          case 'unit_cost':
            Vue.set(this.timeList[0], 'charge3', ele.config_value)
            break
          case 'daily_max_money':
            Vue.set(this.timeList[0], 'charge4', ele.config_value)
            break
        }
      },
      //添加收费标准dialogue
      addForm() {
        this.resetForm()
        this.dialogStatus = 'create'
        this.dialogFormVisible = true
      },
      //添加收费标准
      addSet() {
        let _this = this
        let form = Object.assign({}, _this.form)
        form.ID = 8
        _this.list.push(form)
        _this.dialogFormVisible = false
      },
      //修改收费标准dialogue
      updateForm(row) {
        this.resetForm()
        this.form = row
        this.dialogStatus = 'edit'
        this.dialogFormVisible = true
      },
     //重置表单
      resetForm() {
        this.form = {}
      },
      //编辑收费标准
      editTimeSet(config_class) {
        let _this = this
        let msg = ''
        let count = _this[config_class + '_list'].length
        _this[config_class + '_list'].forEach((ele,index)=>{
          let para = Object.assign({}, ele)
          delete para.update_time
          delete para.create_time
          Vue.set(_this[config_class + '_list'][index], 'loading', true)
          editSysConfig(para).then((res) => {
            if(res.code === 20000 && index === count - 1){
              _this.$message({
                type: 'success',
                message: res.message
              })
            }
          })
        })
        _this.getList()
      },
    }
  }
</script>

<style>
  body .el-table tr:first-child th{
    font-size: 15px;
    color: black;
    border-bottom: 2px solid #999999;
  }
</style>

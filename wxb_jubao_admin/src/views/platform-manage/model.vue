<template>
  <div class="app-container">
    <div class="filter-container">
      <el-row>

        <el-input
          v-model="listQuery.key_word"
          placeholder="关键字"
          style="width: 200px;"
          class="filter-item"
        />
        <el-button

          class="filter-item"
          type="info"
          size="mini"
          style="width: 60px;"
          @click="getList"
        >搜索
        </el-button>
        <el-button
          class="filter-item"
          type="primary"
          size="mini"
          icon="el-icon-plus"
          style="float: right"
          @click="addForm"
        >添加
        </el-button>
      </el-row>
    </div>
            <el-table
              empty-text="-"
              v-loading="listLoading"
      :data="list"
      fit
      highlight-current-row
      style="width: 100%;">
      <el-table-column type="index" width="50"/>
      <el-table-column label="ID" prop="id" align="center"/>
      <el-table-column label="昵称" prop="name" align="center"/>
      <el-table-column label="用户名" prop="username" align="center"/>
      <el-table-column label="角色" prop="roles" align="center">
        <template slot-scope="scope">
          <el-select style="width: 100%;" v-model="scope.row.roles" multiple disabled>
            <el-option disabled :label="i.name" :value="i.id" v-for="(i,index) in role_list" :key="index">
            </el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column label="号码" prop="mobile" align="center"/>
      <el-table-column label="备注" prop="remark" align="center"/>
      <el-table-column label="更新时间" prop="update_time" align="center"/>
      <el-table-column label="创建时间" prop="create_time" align="center"/>

      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200px">
        <template slot-scope="scope">
          <el-button icon="el-icon-edit" class="my-blue" circle size="medium" @click="editForm(scope.row)" ></el-button>
          <el-popover
            placement="top"
            v-if="!(scope.row.STATUS===3)"
            :ref="`popover-${scope.$index}`">
            <p>确定删除此行数据吗？</p>
            <div style="text-align: center; margin: 0">
              <el-button type="primary" size="mini"
                         @click="remove(scope.row),scope._self.$refs[`popover-${scope.$index}`].doClose()">
                确定
              </el-button>
              <el-button type="primary" size="mini"
                         @click="scope._self.$refs[`popover-${scope.$index}`].doClose()">
                取消
              </el-button>
            </div>
            <el-button icon="el-icon-delete" class="my-red" circle size="medium" slot="reference"></el-button>
          </el-popover>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      style="width: 100%;"
      custom-class="dialogwidth">
      <el-form
        ref="dataForm"
        :model="form"
        label-position="left"
        label-width="150px">
        <div class="lb">
          <el-form-item label="昵称：">
            <el-input v-model="form.name"></el-input>
          </el-form-item>
          <el-form-item label="用户名：">
            <el-input v-model="form.username"></el-input>
          </el-form-item>
          <el-form-item label="密码：">
            <el-input v-model="form.password" type="password" show-password></el-input>
          </el-form-item>
          <el-form-item label="角色：">
            <el-select style="width: 100%;" v-model="form.roles" multiple>
              <el-option :label="i.name" :value="i.id" v-for="(i,index) in role_list" :key="index">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="号码：">
            <el-input v-model="form.mobile"></el-input>
          </el-form-item>
          <el-form-item label="过期时间：">
            <el-date-picker v-model="form.EXPIRED_DATE" value-format="yyyy-MM-dd"></el-date-picker>
          </el-form-item>
          <el-form-item label="备注：">
            <el-input v-model="form.remark"></el-input>
          </el-form-item>
        </div>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="dialogStatus==='create'?add():edit()">确定</el-button>
      </div>
    </el-dialog>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.page"
      :page-sizes="[20,50,99]"
      :page-size="99"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />
  </div>
</template>

<script>
  import Pagination from "@/components/Pagination/index"; // secondary package based on el-pagination
  import { getSystemUser, addSystemUser, deleteSystemUser, editSystemUser, getRole } from '@/api/system'
  import Vue from "vue";

  export default {
    components: {Pagination},
    data() {
      return {
        list:[],
        total:0,
        role_list:[],
        listLoading:false,
        form:{},
        dialogFormVisible:false,
        textMap: {
          edit: '编辑',
          create: '添加'
        },
        dialogStatus: '',
        listQuery: {
          page: 1,
          limit: 99,
          start_time: null,
          end_time: null,
        },
      };
    },
    created() {
      this.getList();
    },
    mounted() {
    },
    methods: {
      getList() {
        let _this = this
        _this.listLoading = true;
        getSystemUser(_this.listQuery).then(response => {
          _this.list = response.items;
          _this.total = response.totalCount;
          _this.listLoading = false;
        });
        setTimeout(()=>{
          _this.listLoading = false;
        },3.5*1000)
      },
      add(){
        let _this = this
        let para = Object.assign({},_this.form)
        // para.roles = [para.roles]
        addSystemUser(para).then((res) => {
          if (res.code === 20000) {
            _this.$message({
              type: "success",
              message: res.message,
            });
            _this.dialogFormVisible = false
            _this.getList()
          }
        });

      },
      addForm(){
        this.resetForm()
        this.dialogStatus = 'create'
        this.dialogFormVisible = true
      },
      editForm(row){
        this.resetForm()
        this.form = row
        this.dialogStatus = 'edit'
        this.dialogFormVisible = true
      },
      resetForm(){
        this.form = {}
      },
      edit(){
        let _this = this
        let para = Object.assign({},_this.form)
        // para.roles = [para.roles]
        delete para.update_time
        editSystemUser(para).then((res) => {
          if (res.code === 20000) {
            _this.$message({
              type: "success",
              message: res.message,
            });
            _this.dialogFormVisible = false
            _this.getList()
          }
        });
      },
      remove(row){
        let _this = this
        deleteSystemUser(row.id).then((res) => {
          if (res.code === 20000) {
            _this.$message({
              type: "success",
              message: "删除成功!",
            });
            _this.getList()
          }
        });

      },
    }
  };
</script>

<style>
  body .el-table tr:first-child th{
    font-size: 15px;
    color: black;
    border-bottom: 2px solid #999999;
  }
</style>

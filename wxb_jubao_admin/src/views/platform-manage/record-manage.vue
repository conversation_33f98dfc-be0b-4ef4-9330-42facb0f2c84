<template>
  <div class="app-container">
    <div class="filter-container">
      <el-row>
        <el-col :span="4">
          <el-input
            v-model="listQuery.key_word"
            placeholder="关键字"
            style="width: 90%;margin-left: 5%;"
            class="filter-item"
            clearable
          />
        </el-col>
        <el-col :span="4">
          <el-select v-model="listQuery.filter.status" clearable placeholder="处理状态" style="width: 90%;margin-left: 5%;">
            <el-option v-for="(i,index) in status_list" :key="index" :label="i" :value="index"/>
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select v-model="listQuery.filter.irrigation" clearable placeholder="信息来源"
                     style="width: 90%;margin-left: 5%;">
            <el-option v-for="(i,index) in irrigation_list" :key="index" :label="i" :value="i" v-if="i"/>
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select v-model="listQuery.filter.bigHarmType" clearable placeholder="举报大类"
                     style="width: 90%;margin-left: 5%;">
            <el-option v-for="(i,index) in big_harm_type_list" :key="index" :label="i.label" :value="i.value" v-if="i"/>
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-date-picker
            v-model="listQuery.start_time"
            type="date"
            style="width: 90%;margin-left: 5%;"
            value-format="yyyy-MM-dd"
            placeholder="开始时间"
          />
        </el-col>
        <el-col :span="4">
          <el-date-picker
            v-model="listQuery.end_time"
            type="date"
            style="width: 90%;margin-left: 5%;"
            value-format="yyyy-MM-dd"
            placeholder="结束时间"
          />
        </el-col>

      </el-row>
      <el-row>
        <el-col :span="4">
          <el-button
            class="filter-item"
            type="warning"
            size="mini"
            style="width: 60px;"
            @click="getRecordList"
          >搜索
          </el-button>
          <el-button
            class="filter-item"
            type="info"
            size="mini"
            style="width: 60px;"
            @click="resetFilter"
          >重置
          </el-button>
        </el-col>
        <el-button
          class="filter-item"
          type="info"
          icon="el-icon-download"
          size="mini"
          style="float: right;"
          @click="exportTable"
        >导出报表
        </el-button>
      </el-row>
    </div>
    <el-table
      v-loading="listLoading"
      empty-text="-"
      :data="list"
      fit
      size="mini"
      :row-class-name="tableRowClassName"
      highlight-current-row
      style="width: 100%;">
      <el-table-column type="index" width="50"/>
      <el-table-column
        v-for="(ctl,index) in table_ctl"
        v-if="ctl.visiable"
        :key="index"
        :formatter="ctl.formatter"
        :sortable="ctl.sortable"
        :label="ctl.label"
        :prop="ctl.prop"
        align="center"
        :width="ctl.width?ctl.width:''"
      />

      <!--      <el-table-column label="状态">-->
      <!--        <template slot-scope="scope">-->
      <!--          <span v-if="scope.row.status == 0" style="margin-right: 15px">未处理</span>-->
      <!--          <span v-if="scope.row.status == 1" style="margin-right: 15px">已处理</span>-->
      <!--          <span v-if="scope.row.status == 2" style="margin-right: 15px">已拒绝</span>-->
      <!--        </template>-->
      <!--      </el-table-column>-->

      <el-table-column label="操作" align="center" class-name="small-padding " width="100px">
        <template slot="header" slot-scope="scope">
          <span style="margin-right: 15px">操作</span>
          <el-tooltip class="item" effect="dark" content="设置列" placement="top">
            <el-button icon="el-icon-setting" circle type="text" @click="table_dialog_form_visiable = true"/>
          </el-tooltip>
        </template>
        <template slot-scope="scope">
          <el-tooltip class="item" effect="dark" content="编辑" placement="top" v-if="permission.edit">
            <el-button icon="el-icon-edit" class="my-blue" circle size="medium"
                       @click="editRecordForm(scope.row)"></el-button>
          </el-tooltip>
          <el-tooltip v-if="permission.delete" class="item" effect="dark" :hide-after="500" content="删除"
                      placement="top">
            <el-button icon="el-icon-delete" class="my-red" circle size="medium" @click="showDeleteForm(scope.row)"/>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      style="width: 100%;"
      custom-class="dialogwidth"
    >
      <el-form
        ref="dataForm"
        :model="form"
        label-position="left"
        label-width="150px"
      >
        <div class="lb">
          <el-form-item label="ID：">
            <el-input readonly v-model="form.id"/>
          </el-form-item>

          <el-form-item label="处理结果：">
            <el-select v-model="form.status" style="width: 100%;" @change="handleStatusChange()">
              <el-option v-for="(i,index) in status_list" :key="index" :label="i" :value="index"/>
            </el-select>
          </el-form-item>

          <el-form-item label="回复网民：">
            <el-select v-if="!otherStatusVisible" v-model="form.status_lv2" style="width: 100%;"
                       @change="handleStatusLv2Change()">
              <el-option v-for="(i,index) in status_lv2_list[form.status]" :key="index" :label="i" :value="i"/>
            </el-select>
            <el-input v-model="form.status_lv2" placeholder="请输入其他处理结果" v-if="otherStatusVisible"/>
          </el-form-item>


          <el-form-item label="真实姓名：">
            <el-input readonly v-model="form.realname"/>
          </el-form-item>
          <el-form-item label="邮箱：">
            <el-input readonly v-model="form.email"/>
          </el-form-item>
          <el-form-item label="查询码：">
            <el-input readonly v-model="form.number"/>
          </el-form-item>

          <el-form-item label="标题：">
            <el-input readonly v-model="form.title"/>
          </el-form-item>
          <el-form-item label="举报对象：">
            <el-input readonly v-model="form.targetName"/>
          </el-form-item>
          <el-form-item label="举报网址：">
            <el-input readonly v-model="form.targetUrl"/>
          </el-form-item>
          <el-form-item label="举报渠道：">
            <!--            <el-input v-model="form.irrigation"/>-->
            <el-select v-model="form.irrigation" style="width: 100%;">
              <el-option v-for="(i,index) in irrigation_list" :key="index" :label="i" :value="i"/>
            </el-select>
          </el-form-item>
          <el-form-item label="举报大类：">
            <el-select v-model="form.bigHarmType" style="width: 100%;" disabled>
              <el-option v-for="(i,index) in big_harm_type_list" :key="index" :label="i.label" :value="i.value"/>
            </el-select>
          </el-form-item>

          <el-form-item label="企业名称：" v-if="form.bigHarmType === '012'">
            <el-input readonly v-model="form.entName"/>
          </el-form-item>

          <el-form-item label="举报对象账户：" v-if="form.bigHarmType === '012'">
            <el-input readonly v-model="form.targetAccount"/>
          </el-form-item>
          <el-form-item label="企业类型：" v-if="form.bigHarmType === '012'">
            <el-select v-model="form.entType" style="width: 100%;" disabled>
              <el-option v-for="(i,index) in ent_type_list" :key="index" :label="i.text" :value="i.value"/>
            </el-select>
          </el-form-item>
          <el-form-item label="企业性质：" v-if="form.bigHarmType === '012'">
            <el-select v-model="form.entNature" style="width: 100%;" disabled>
              <el-option v-for="(i,index) in ent_nature_list" :key="index" :label="i.text" :value="i.value"/>
            </el-select>
          </el-form-item>
          <el-form-item label="行业分类：" v-if="form.bigHarmType === '012'">
            <el-select v-model="form.entIndustry" style="width: 100%;" disabled>
              <el-option v-for="(i,index) in ent_industry_list" :key="index" :label="i.text" :value="i.value"/>
            </el-select>
          </el-form-item>
          <el-form-item label="企业联系人：" v-if="form.bigHarmType === '012'">
            <el-select v-model="form.entContactType" style="width: 100%;" disabled>
              <el-option v-for="(i,index) in ent_contact_type_list" :key="index" :label="i.text" :value="i.value"/>
            </el-select>
          </el-form-item>
          <!--          <el-form-item label="密码：">-->
          <!--            <el-input v-model="form.password" type="password" show-password/>-->
          <!--          </el-form-item>-->
          <!--          <el-form-item label="角色：">-->
          <!--            <el-select v-model="form.roles" style="width: 100%;" multiple>-->
          <!--              <el-option v-for="(i,index) in role_list" :key="index" :label="i.name" :value="i.id"/>-->
          <!--            </el-select>-->
          <!--          </el-form-item>-->
          <!--          <el-form-item label="号码：">-->
          <!--            <el-input v-model="form.mobile"/>-->
          <!--          </el-form-item>-->
          <!--          <el-form-item label="过期时间：">-->
          <!--            <el-date-picker v-model="form.EXPIRED_DATE" style="width: 100%" value-format="yyyy-MM-dd"/>-->
          <!--          </el-form-item>-->
          <!--          <el-form-item label="备注：">-->
          <!--            <el-input v-model="form.remark"/>-->
          <!--          </el-form-item>-->
        </div>

        <div class="sl">

          <el-form-item label="证据类型：" v-if="form.bigHarmType === '012'">
            <el-select v-model="form.entProofType" style="width: 100%;" disabled multiple>
              <el-option v-for="(i,index) in ent_proof_type_list" :key="index" :label="i.text" :value="i.value"/>
            </el-select>
          </el-form-item>
          <el-form-item label="举报内容：">
            <el-input type="textarea" readonly v-model="form.content"/>
          </el-form-item>


          <el-form-item label="举报材料：">
            <span>{{form.file_path}}</span>
            <el-button @click="downloadFile(form.file_path)">点击下载</el-button>

          </el-form-item>

          <el-form-item label="处理结果：">
            <el-input type="textarea" v-model="form.process_result"/>
            <el-button v-if="form.process_result_file_path" @click="downloadFile(form.process_result_file_path)"
                       style="margin-top: 8px">下载附件
            </el-button>
            <el-button type="danger" v-if="form.process_result_file_path" @click="deleteResultFile(form.id)"
                       style="margin-top: 8px">删除附件
            </el-button>
          </el-form-item>

          <el-upload
            class="upload-demo"
            :action="serverUrl+'record_manage/upload_file'"
            :on-preview="handlePreview"
            :on-success="handleSuccess"
            :data="form"
            multiple
            :limit="3"
            :on-exceed="handleExceed"
            :file-list="fileList">
            <el-button size="small" type="primary">上传处理结果附件</el-button>
            <span style="color:red">提示: 只能上传一个附件,如果有多个文件需要上传请压缩成zip等压缩文件</span>
          </el-upload>


        </div>


      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="dialogStatus==='create'?addRecord():editRecord()">确定</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="表格列编辑"
      :visible.sync="table_dialog_form_visiable"
      style="width: 100%;"
      custom-class="dialogwidth"
    >
      <el-form
        ref="dataForm"
        label-position="left"
        label-width="200px"
      >
        <div class="lb">
          <el-form-item v-for="(ctl,index) in table_ctl" :key="index" :label="ctl.label + '：'">
            <el-switch v-model="ctl.visiable" active-text="可见" inactive-text="不可见" active-color="#13ce66"
                       inactive-color="#666666"/>
          </el-form-item>
        </div>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="table_dialog_form_visiable = false">确定</el-button>
      </div>
    </el-dialog>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.page"
      :page-sizes="[20,50,99]"
      :page-size="99"
      :limit.sync="listQuery.limit"
      @pagination="getRecordList"
    />
  </div>
</template>

<script>
  import Pagination from '@/components/Pagination/index' // secondary package based on el-pagination
  import { getPermission } from '@/utils/permission'
  import { getParkRecord, addParkRecord, editParkRecord, removeParkRecord, removeRecordfile } from '@/api/manage'
  import config from "@/utils/config";

  import Vue from 'vue'

  export default {
    components: { Pagination },
    data() {
      return {
        serverUrl: '',
        list: [],
        total: 0,
        role_list: [],
        routeID: 33,
        permission: {},
        listLoading: false,
        fileList: [],
        form: {},
        dialogFormVisible: false,
        otherStatusVisible: false,
        table_dialog_form_visiable: false,
        ent_type_list:config('entType'),
        ent_nature_list:config('entNature'),
        ent_industry_list:config('entIndustry'),
        ent_contact_type_list:config('entContactType'),
        ent_proof_type_list:config('entProofType'),
        pay_type_list: ['线上⽀付', '线下扫码⽀付', '线下现⾦⽀付', '商户优惠', '月租', '公务车'],
        status_list: config('status_list'),
        status_lv2_list: {
          0: ['核查中', '已转交相关部门处置(相关部门正在研处中', '其他'],
          1: ['已取消网站备案,但网站IP地址在境外,需封堵处置', '其他'],
          2: ['已转交相关部门处置（已处置）', '已对应用程序进行处置', '已通知删除有害（违法、不良等）信息', '已通知关闭网站', '已对账号进行处置', '其他'],
          3: ['举报要件，相关证明、证据等材料不足', '所提供的网站链接无效', '未发现所举报的有害（违法、不良等）信息内容', '其他'],
          4: ['（转央办（国家）举报中心）', '经核查，所举报信息网址非属地管理，请到12377平台举报网址：https://www.12377.cn', '其他']
        },
        irrigation_list: ['网评员录入', '网友举报'],
        big_harm_type_list: config('bigHarmType'),
        table_ctl: config('record_table_ctl'),
        textMap: {
          edit: '编辑记录',
          create: '添加记录'
        },
        dialogStatus: '',
        listQuery: {
          page: 1,
          limit: 99,
          filter: {}
        }
      }
    },
    created() {
      this.getRecordList()
    },
    mounted() {
      this.permission = getPermission(this.routeID)
      this.serverUrl = process.env.VUE_APP_BASE_API
    },
    methods: {
      handleStatusLv2Change() {
        var _this = this
        if (_this.form.status_lv2 == '其他') {
          this.form.status_lv2 = ''
          _this.otherStatusVisible = true
        }
      },
      handleSuccess() {
        this.getRecordList()
        this.dialogFormVisible = false;
        this.$message.success('添加物料成功')
      },
      handleStatusChange() {
        this.otherStatusVisible = false
        this.form.status_lv2 = ''
        console.log(this.status_lv2_list[this.form.status])
      },
      //删除附件地址
      deleteResultFile(id) {
        const _this = this
        const para = {
          id: id
        }
        // para.roles = [para.roles]
        // delete para.update_time
        removeRecordfile(para).then((res) => {
          if (res.code === 20000) {
            _this.$message({
              type: 'success',
              message: res.message
            })
            _this.dialogFormVisible = false
            _this.getRecordList()
          }
        })
      },
      handlePreview(file) {
        console.log(file)
      },
      handleExceed(files, fileList) {
        this.$message.warning(`当前限制选择 3 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`)
      },
      beforeRemove(file, fileList) {
        return this.$confirm(`确定移除 ${file.name}？`)
      },
      // 获取记录停车数据
      downloadFile(file_path) {
        window.open(file_path)
      },
      getRecordList() {
        const _this = this
        _this.listLoading = true
        let para = Object.assign({}, _this.listQuery)
        para = _this.clearObj(para)
        getParkRecord(para).then(response => {
          _this.list = response.items
          _this.total = response.totalCount
          _this.listLoading = false
        })
      },
      // 添加停车记录
      addRecord() {
        const _this = this
        const para = Object.assign({}, _this.form)
        // para.roles = [para.roles]
        addParkRecord(para).then((res) => {
          if (res.code === 20000) {
            _this.$message({
              type: 'success',
              message: res.message
            })
            _this.dialogFormVisible = false
            _this.getRecordList()
          }
        })
      },
      // 显示添加停车记录dialogue
      addRecordForm() {
        this.resetForm()
        this.dialogStatus = 'create'
        this.dialogFormVisible = true
      },
      // 显示编辑停车记录dialogue
      editRecordForm(row) {
        this.resetForm()
        this.form = Object.assign({}, row)
        if(this.form.bigHarmType ==='012'){
          // this.form.entProofType = this.form.entProofType.split(',')
          this.$set(this.form,'entProofType',this.form.entProofType.split(','))
        }


        this.dialogStatus = 'edit'
        this.dialogFormVisible = true
      },
      // 重置表单
      resetForm() {
        this.form = {}
      },
      // 编辑停车记录
      editRecord() {
        const _this = this
        const para = {
          id: _this.form.id,
          status: _this.form.status,
          status_lv2: _this.form.status_lv2,
          process_result: _this.form.process_result,
          irrigation: _this.form.irrigation
        }
        // para.roles = [para.roles]
        // delete para.update_time
        editParkRecord(para).then((res) => {
          if (res.code === 20000) {
            _this.$message({
              type: 'success',
              message: res.message
            })
            _this.dialogFormVisible = false
            _this.getRecordList()
          }
        })
      },
      // 删除停车记录
      removeRecord(row) {
        const _this = this
        removeParkRecord(row.id).then((res) => {
          if (res.code === 20000) {
            _this.$message({
              type: 'success',
              message: '删除成功!'
            })
            _this.getRecordList()
          }
        })
      },
      // 格式化表格数据
      // 显示删除弹窗
      showDeleteForm(row) {
        this.$confirm('确定删除举报记录「ID:' + row.id + '」 「真实姓名：' + row.realname + '」吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.removeRecord(row)
        }).catch(() => {
        })
      },
      // 导出表格
      exportTable() {
        const _this = this
        const para = _this.clearObj(Object.assign({}, this.listQuery))
        delete para.limit
        delete para.page
        para.export = 1
        // exportFile('business', para)
        getParkRecord(para).then(res => {
          // console.log(res)
          if (res.data.code === 50001) {
            this.$message({
              type: 'error',
              message: res.data.message,
              duration: 2500
            })
            return
          }
          if (res.data.type) {
            // 文件下载
            const blob = new Blob([res.data], {
              type: 'application/vnd.ms-excel'
            })
            let link = document.createElement('a')
            link.href = URL.createObjectURL(blob)
            link.setAttribute('download', '导出文件.xlsx')
            link.click()
            link = null
          } else {
            this.$message({
              type: 'error',
              message: '导出出错',
              duration: 2500
            })
            // 返回json
          }
        })
      },
      // 重置表单数据
      resetFilter() {
        this.listQuery = this.$options.data().listQuery
        this.getRecordList()
      },
      // 去除无用数据
      clearObj(obj) {
        for (var ob in obj) {
          if (obj[ob] == null || obj[ob] === '') {
            delete obj[ob]
          } else if (typeof obj[ob] === 'object') {
            obj[ob] = this.clearObj(obj[ob])
          }
        }
        return obj
      },
      tableRowClassName({ row, rowIndex }) {
        if (rowIndex % 2 === 0) {
          return 'success-row'
        }
        return ''
      }
    }
  }
</script>

<style>
  body .el-table tr:first-child th {
    font-size: 15px;
    color: black;
    border-bottom: 2px solid #999999;
  }

  .el-table .warning-row {
    background: oldlace;
  }

  .el-table .success-row {
    background: #f0f9eb;
  }

  .sl {
    margin-left: 50px;
    margin-right: 50px;
  }
</style>

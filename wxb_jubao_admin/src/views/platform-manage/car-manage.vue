<template>
  <div class="app-container">
    <div class="filter-container">
      <el-row>

        <el-input
          v-model="listQuery.key_word"
          placeholder="关键字"
          style="width: 200px;"
          class="filter-item"
        />
        <el-button

          class="filter-item"
          type="info"
          size="mini"
          style="width: 60px;"
          @click="getCarList"
        >搜索
        </el-button>
        <el-button
          class="filter-item"
          type="primary"
          size="mini"
          icon="el-icon-plus"
          style="float: right"
          @click="addCarForm"
        >添加车辆
        </el-button>

      </el-row>
    </div>
    <el-table
      empty-text="-"
      v-loading="listLoading"
      :data="list"
      fit
      highlight-current-row
      style="width: 100%;">
      <el-table-column type="index" width="50"/>
      <el-table-column v-for="(ctl,index) in table_ctl" :formatter="ctl.formatter" :sortable="ctl.sortable" :key="index" :label="ctl.label" :prop="ctl.prop" v-if="ctl.visiable" align="center"></el-table-column>

      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200px">
        <template slot="header" slot-scope="scope">
          <span style="margin-right: 15px">操作</span>
          <el-tooltip class="item" effect="dark" :hide-after="500" content="设置列" placement="top">
            <el-button icon="el-icon-setting" @click="table_dialog_form_visiable = true" circle type="text"></el-button>
          </el-tooltip>
        </template>
        <template slot-scope="scope">
          <el-tooltip class="item" effect="dark" :hide-after="500" content="编辑" placement="top">
            <el-button icon="el-icon-edit" class="my-blue" circle size="medium" @click="editCarForm(scope.row)"></el-button>
          </el-tooltip>
          <el-tooltip class="item" effect="dark" :hide-after="500" content="删除" placement="top">
            <el-button icon="el-icon-delete" class="my-red" circle size="medium" @click="showDeleteForm(scope.row)"></el-button>
          </el-tooltip>
        </template>
      </el-table-column>

    </el-table>

    <el-dialog
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      style="width: 100%;"
      custom-class="dialogwidth">
      <el-form
        ref="dataForm"
        :model="form"
        label-position="left"
        label-width="200px">
        <div class="lb">
          <el-form-item label="车牌号：">
            <el-input v-model="form.plate_number"></el-input>
          </el-form-item>
          <el-form-item label="车主姓名：">
            <el-input v-model="form.owner"></el-input>
          </el-form-item>
          <el-form-item label="所属单位：">
            <el-input v-model="form.unit"></el-input>
          </el-form-item>
          <el-form-item label="手机号：">
            <el-input v-model="form.mobile"></el-input>
          </el-form-item>
          <el-form-item label="类型：">
            <el-select style="width: 100%;" v-model="form.car_type">
              <el-option :label="key" :value="index" v-for="(key,index) in car_type" :key="key">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="状态：">
            <el-select style="width: 100%;" v-model="form.status">
              <el-option :label="key" :value="value" v-for="(key,value) in car_status" :key="key">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="有效期：">
            <el-date-picker v-model="form.valid_to" value-format="yyyy-MM-dd"></el-date-picker>
          </el-form-item>
          <el-form-item label="备注：">
            <el-input v-model="form.remark"></el-input>
          </el-form-item>
        </div>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="dialogStatus==='create'?addCar():editCar()">确定</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="表格列编辑"
      :visible.sync="table_dialog_form_visiable"
      style="width: 100%;"
      custom-class="dialogwidth">
      <el-form
        ref="dataForm"
        label-position="left"
        label-width="200px">
        <div class="lb">
          <el-form-item :label="ctl.label + '：'" v-for="(ctl,index) in table_ctl" :key="index">
            <el-switch v-model="ctl.visiable" active-text="可见" inactive-text="不可见" active-color="#13ce66" inactive-color="#666666"></el-switch>
          </el-form-item>
        </div>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="table_dialog_form_visiable = false" type='primary'>确定</el-button>
      </div>
    </el-dialog>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.page"
      :page-sizes="[20,50,99]"
      :page-size="99"
      :limit.sync="listQuery.limit"
      @pagination="getCarList"
    />
  </div>
</template>

<script>
  import Pagination from '@/components/Pagination/index' // secondary package based on el-pagination
  import {getCar, getCarDetail, editCar, removeCar, addCar} from '@/api/manage'
  import Vue from 'vue'

  export default {
    components: {Pagination},
    data() {
      return {

        list: [],
        car_option: ['小汽车', '大型机动车',],
        car_type: ['普通车辆',  '公务车'],

        car_status: ['正常',  '黑名单', '白名单'],
        table_dialog_form_visiable: false,
        table_ctl: [
          {
            label: 'ID',
            prop: 'id',
            sortable: false,
            visiable: true,
          },
          {
            label: '车牌号',
            prop: 'plate_number',
            sortable: false,
            visiable: true,
          },
          {
            label: '车主姓名',
            prop: 'owner',
            sortable: false,
            visiable: true,
          },
          {
            label: '所属单位',
            prop: 'unit',
            sortable: false,
            visiable: true,
          },
          {
            label: '手机号',
            prop: 'mobile',
            sortable: false,
            visiable: true,
          },
          {
            label: '类型',
            prop: 'car_type',
            sortable: false,
            visiable: true,
            formatter: this.tableFormat,
          },
          {
            label: '有效期',
            prop: 'valid_to',
            sortable: false,
            visiable: true,
          },
          {
            label: '状态',
            prop: 'status',
            sortable: false,
            visiable: true,
            formatter: this.tableFormat,
          },
          {
            label: '创建时间',
            prop: 'create_time',
            sortable: false,
            visiable: true,
            formatter: this.tableFormat,
          },
          {
            label: '更新时间',
            prop: 'update_time',
            sortable: false,
            visiable: true,
            formatter: this.tableFormat,
          },
          {
            label: '备注',
            prop: 'remark',
            sortable: false,
            visiable: true,
          },
        ],
        total: 0,
        table_keyword: '',
        listLoading: false,
        form: {},
        dialogTimeFormVisible: false,
        dialogFormVisible: false,
        zero: '0',
        textMap: {
          edit: '编辑车辆',
          create: '添加车辆'
        },
        timeSetList: [30, 120, 180],
        dialogStatus: '',
        listQuery: {
          page: 1,
          limit: 99,
          start_time: null,
          end_time: null
        }
      }
    },
    created() {
      this.getCarList()
    },
    mounted() {
    },
    methods: {
      //获取车辆数据
      getCarList() {
        let _this = this
        _this.listLoading = true;
        getCar(_this.listQuery).then(response => {
          _this.list = response.items;
          _this.total = response.totalCount;
          _this.listLoading = false;
        });
        setTimeout(() => {
          _this.listLoading = false;
        }, 3.5 * 1000)
      },
      //显示添加 车辆dialogue
      addCarForm() {
        this.resetForm()
        this.dialogStatus = 'create'
        this.dialogFormVisible = true
      },
      //添加车辆
      addCar() {
        let _this = this
        let para = Object.assign({}, _this.form)
        addCar(para).then((res) => {
          if (res.code === 20000) {
            _this.$message({
              type: "success",
              message: res.message,
            });
            _this.dialogFormVisible = false
            _this.getCarList()
          }
        });
      },
      //显示修改车辆dialogue
      editCarForm(row) {
        this.resetForm()
        this.form = row
        this.dialogStatus = 'edit'
        this.dialogFormVisible = true
      },
      //修改车辆
      editCar() {
        let _this = this
        let para = Object.assign({}, _this.form)
        delete para.update_time
        editCar(para).then((res) => {
          if (res.code === 20000) {
            _this.$message({
              type: "success",
              message: res.message,
            });
            _this.dialogFormVisible = false
            _this.getCarList()
          }
        });
      },
      //重置表单
      resetForm() {
        this.form = {}
      },
      //删除车辆
      removeCar(row) {
        let _this = this
        removeCar(row.id).then((res) => {
          if (res.code === 20000) {
            _this.$message({
              type: "success",
              message: "删除成功!",
            });
            _this.getCarList()
          }
        });
      },
      // 显示删除弹窗
      showDeleteForm(row) {
        this.$confirm('确定删除车辆「' + row.name + '」吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.removeCar(row)
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除',
            duration: 500,
          })
        })
      },
      // 格式化表格数据
      tableFormat(row, column, index) {
        let str = ''
        switch (column.property) {
          case 'update_time':
            str = row.update_time ? row.update_time.slice(0, 10) : ''
            break
          case 'create_time':
            str = row.create_time.slice(0, 10)
            break
          case 'car_type':
            str = this.car_type[index]
            break
          case 'status':
            str = this.car_status[index]
            break
        }
        return str
      },
    }
  }
</script>

<style>
  body .el-table tr:first-child th {
    font-size: 15px;
    color: black;
    border-bottom: 2px solid #999999;
  }
</style>

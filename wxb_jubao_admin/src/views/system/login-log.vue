<template>
  <div class="app-container">
    <div class="filter-container">
      <el-row>

        <el-input
          v-model="listQuery.key_word"
          placeholder="关键字"
          style="width: 200px;"
          class="filter-item"
        />
        <el-button

          class="filter-item"
          type="info"
          size="mini"
          style="width: 60px;"
          @click="getLoginLogList"
        >搜索
        </el-button>
      </el-row>
    </div>
            <el-table
              empty-text="-"
              v-loading="listLoading"
      :data="list"
      fit
      highlight-current-row
      style="width: 100%;">
      <el-table-column type="index" width="50"/>
      <el-table-column v-for="(ctl,index) in table_ctl" :sortable="ctl.sortable" :key="index" :label="ctl.label" :prop="ctl.prop" v-if="ctl.visiable" align="center"></el-table-column>
      <el-table-column label="创建时间" prop="create_time" align="center" class-name="small-padding fixed-width">
        <template slot="header" slot-scope="scope">
          <div>
            <span style="margin-right: 5px">创建时间</span>
            <el-tooltip class="item" effect="dark" :hide-after="500" content="设置列" placement="top">
              <el-button icon="el-icon-setting" @click="table_dialog_form_visiable = true" circle type="text"></el-button>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog
      title="表格列编辑"
      :visible.sync="table_dialog_form_visiable"
      style="width: 100%;"
      custom-class="dialogwidth">
      <el-form
        ref="dataForm"
        label-position="left"
        label-width="200px">
        <div class="lb">
          <el-form-item :label="ctl.label + '：'" v-for="(ctl,index) in table_ctl" :key="index">
            <el-switch v-model="ctl.visiable" active-text="可见" inactive-text="不可见" active-color="#13ce66" inactive-color="#666666"></el-switch>
          </el-form-item>
        </div>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="table_dialog_form_visiable = false" type='primary'>确定</el-button>
      </div>
    </el-dialog>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.page"
      :page-sizes="[20,50,99]"
      :page-size="99"
      :limit.sync="listQuery.limit"
      @pagination="getLoginLogList"
    />
  </div>
</template>

<script>
  import Pagination from "@/components/Pagination/index"; // secondary package based on el-pagination
  import { getLoginLog,addLoginLog,editLoginLog,removeLoginLog} from '@/api/system'
  import Vue from "vue";

  export default {
    components: {Pagination},
    data() {
      return {
        list:[],
        total:0,
        role_list:[],
        listLoading:false,
        form:{},
        dialogFormVisible:false,
        table_dialog_form_visiable:false,
        table_ctl:[
          {
            label:'ID',
            prop:'id',
            sortable: false,
            visiable:true,
            formatter:null,
          },
          {
            label:'用户ID',
            prop :'user_id',
            sortable: false,
            visiable:true,
            formatter:null,
          },
          {
            label:'用户',
            prop :'name',
            sortable: false,
            visiable:true,
            formatter:null,
          },
          {
            label:'最近登录地点',
            prop :'place',
            sortable: false,
            visiable:true,
            formatter:null,
          },
          {
            label:'最近登录IP',
            prop :'ip',
            sortable: false,
            visiable:true,
            formatter:null,
          },
          // {
          //   label:'创建时间',
          //   prop:'create_time',
          //   sortable: false,
          //   visiable:true,
          //   formatter:null,
          // },
        ],
        textMap: {
          edit: '编辑',
          create: '添加'
        },
        dialogStatus: '',
        listQuery: {
          page: 1,
          limit: 99,
          start_time: null,
          end_time: null,
        },
      };
    },
    created() {
      this.getLoginLogList();
    },
    mounted() {
    },
    methods: {
      //获取登陆日志
      getLoginLogList() {
        let _this = this
        _this.listLoading = true;
        getLoginLog(_this.listQuery).then(response => {
          _this.list = response.items;
          _this.total = response.totalCount;
          _this.listLoading = false;
        });
        setTimeout(()=>{
          _this.listLoading = false;
        },3.5*1000)
      },
    }
  };
</script>
<style>
  body .el-table tr:first-child th{
    font-size: 15px;
    color: black;
    border-bottom: 2px solid #999999;
  }
</style>

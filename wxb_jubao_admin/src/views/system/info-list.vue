<template>
  <div class="app-container">
    <div class="filter-container">
      <el-row>
        <el-input
          v-model="listQuery.key_word"
          placeholder="关键字"
          style="width: 200px;"
          clearable
          class="filter-item"
        />
        <el-button
          class="filter-item"
          type="info"
          size="mini"
          style="width: 60px;"
          @click="getInfoList"
        >搜索
        </el-button>
        <el-button
          class="filter-item"
          type="primary"
          icon="el-icon-plus"
          style="float: right"
          size="mini"
          @click="addInfoForm"
        >添加信息
        </el-button>
      </el-row>
    </div>
    <el-table
      empty-text="-"
      v-loading="listLoading"
      :data="list"
      fit
      highlight-current-row
      style="width: 100%;">
      <el-table-column type="index" width="50"/>
      <el-table-column v-for="(ctl,index) in table_ctl" :formatter="ctl.formatter" :sortable="ctl.sortable" :key="index" :label="ctl.label" :prop="ctl.prop" v-if="ctl.visiable"
                       align="center"></el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200px">
        <template slot="header" slot-scope="scope">
          <span style="margin-right: 15px">操作</span>
          <el-tooltip class="item" effect="dark" :hide-after="500" content="设置列" placement="top">
            <el-button icon="el-icon-setting" @click="table_dialog_form_visiable = true" circle type="text"></el-button>
          </el-tooltip>
        </template>
        <template slot-scope="scope">
          <el-tooltip class="item" effect="dark" :hide-after="500" content="编辑" placement="top">
            <el-button icon="el-icon-edit" class="my-blue" circle size="medium" @click="updateInfoForm(scope.row)"></el-button>
          </el-tooltip>
          <el-tooltip class="item" effect="dark" :hide-after="500" content="删除" placement="top">
            <el-button icon="el-icon-delete" class="my-red" circle size="medium" @click="showDeleteForm(scope.row)"></el-button>
          </el-tooltip>
        </template>
      </el-table-column>

    </el-table>

    <el-dialog
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      style="width: 100%;"
      custom-class="dialogwidth">
      <el-form
        ref="dataForm"
        :model="form"
        label-position="left"
        label-width="150px">
        <div class="lb">
          <el-form-item label="标题：">
            <el-input v-model="form.title"></el-input>
          </el-form-item>
          <el-form-item label="类型：">
            <el-select clearable placeholder="类型" style="width: 100%;" v-model="form.type" filterable>
              <el-option :label="i" :value="index" v-for="(i,index) in tyle_list" :key="index">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="是否隐藏：">
            <el-switch v-model="form.status" :active-value="1" :inactive-value="0"></el-switch>
          </el-form-item>
        </div>
        <div class="sb">
          <el-form-item label="内容：">
            <el-input type="textarea" :autosize="{ minRows: 4}" v-model="form.content" style="width: 100%;"></el-input>
          </el-form-item>
        </div>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="dialogStatus==='create'?addInfo():editInfo()">确定</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="表格列编辑"
      :visible.sync="table_dialog_form_visiable"
      style="width: 100%;"
      custom-class="dialogwidth">
      <el-form
        ref="dataForm"
        label-position="left"
        label-width="200px">
        <div class="lb">
          <el-form-item :label="ctl.label + '：'" v-for="(ctl,index) in table_ctl" :key="index">
            <el-switch v-model="ctl.visiable" active-text="可见" inactive-text="不可见" active-color="#13ce66" inactive-color="#666666"></el-switch>
          </el-form-item>
        </div>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="table_dialog_form_visiable = false" type='primary'>确定</el-button>
      </div>
    </el-dialog>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.page"
      :page-sizes="[20,50,99]"
      :page-size="99"
      :limit.sync="listQuery.limit"
      @pagination="getInfoList"
    />
  </div>
</template>

<script>
  import Pagination from "@/components/Pagination/index"; // secondary package based on el-pagination
  import XLSX from 'xlsx'
  import Vue from "vue";
  import {getInfo, getInfoDetail, addInfo, editInfo, removeInfo} from '@/api/system'
  import {getSysConfig} from '@/api/system'

  export default {
    components: {Pagination},
    data() {
      return {
        list: [],
        total: 0,
        rechargeForm: {},
        user_monthly: {},
        fileList: [],
        tyle_list:['网信信息正常','安全预警','警示案例','法律','行政法规','部门规章','司法解释','规范性文件','政策文件'],
        excelLoading: false,
        table_dialog_form_visiable: false,
        excelData: {
          header: null,
          results: null
        },
        table_ctl: [
          {
            label: 'ID',
            prop: 'id',
            sortable: false,
            visiable: true,
            formatter: null,
          },
          {
            label: '标题',
            prop: 'title',
            sortable: false,
            visiable: true,
            formatter: null,
          },
          {
            label: '网信信息内容',
            prop: 'content',
            sortable: false,
            visiable: true,
            formatter: this.tableFormat,
          },
          // {
          //   label:'手机',
          //   prop:'user_phone',
          //   sortable: false,
          //   visiable:true,
          //   formatter:null,
          // },
          {
            label: '状态',
            prop: 'status',
            sortable: false,
            visiable: true,
            formatter: this.tableFormat,
          },

          {
            label: '更新时间',
            prop: 'update_time',
            sortable: false,
            visiable: true,
            formatter: this.tableFormat,
          },
          {
            label: '创建时间',
            prop: 'create_time',
            sortable: false,
            visiable: true,
            formatter: this.tableFormat,
          },
        ],
        total_price: 0,
        baseUrl: process.env.VUE_APP_BASE_API,
        package_type_list: [
          {value: 1, label: '1个月'}, {value: 3, label: '3个月'}, {value: 6, label: '6个月'}, {value: 12, label: '12个月'}],
        rechargeFormVisible: false,
        role_list: ['商家', '管理员',],
        listLoading: false,
        form: {},
        dialogFormVisible: false,
        textMap: {
          edit: '编辑用户',
          create: '添加用户'
        },
        dialogStatus: '',
        listQuery: {
          page: 1,
          limit: 99,
          start_time: null,
          end_time: null,
        },
      };
    },
    created() {
      this.getInfoList();
    },
    mounted() {
    },
    methods: {
      //导出表格
      exportTable() {
        let _this = this
        let para = {
          export: 1
        }
        exportCountReport(para).then(res => {
          console.log(res)
          if (res.data.code === 50001) {
            _this.$message({
              type: 'error',
              message: res.data.message,
              duration: 2500
            })
            return
          }
          if (res.data.type) {
            // 文件下载
            const blob = new Blob([res.data], {
              type: "application/vnd.ms-excel"
            });
            let link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.setAttribute('download', '导出文件.xlsx');
            link.click();
            link = null;
          } else {
            _this.$message({
              type: 'error',
              message: '导出出错',
              duration: 2500
            })
            // 返回json
          }
        })

      },
      //获取网信信息数据
      getInfoList() {
        let _this = this
        _this.listLoading = true
        getInfo(_this.listQuery).then(response => {
          _this.list = response.items;
          _this.total = response.totalCount;
          _this.listLoading = false
        });
      },
      //添加网信信息
      addInfo() {
        let _this = this
        let para = Object.assign({}, _this.form)
        // para.roles = [para.roles]
        addInfo(para).then((res) => {
          if (res.code === 20000) {
            _this.$message({
              type: "success",
              message: '添加成功',
            });
            _this.dialogFormVisible = false
            _this.getInfoList()
          }
        });
      },
      //显示添加网信信息dialogue
      addInfoForm() {
        this.resetForm()
        this.dialogStatus = 'create'
        this.dialogFormVisible = true
      },
      //显示修改网信信息dialogue
      updateInfoForm(row) {
        this.resetForm()
        this.form = row
        this.dialogStatus = 'edit'
        this.dialogFormVisible = true
      },
      //充值表单数据
      resetForm() {
        this.form = {}
        this.rechargeForm = {valid_months: 1, valid: true}
      },
      //修改网信信息
      editInfo() {
        let _this = this
        let para = Object.assign({}, _this.form)
        // para.roles = [para.roles]
        delete para.update_time
        editInfo(para).then((res) => {
          if (res.code === 20000) {
            _this.$message({
              type: "success",
              message: '修改成功！',
            });
            _this.dialogFormVisible = false
            _this.getInfoList()
          }
        });
      },
      //删除网信信息
      removeInfo(row) {
        let _this = this
        removeInfo(row.id).then((res) => {
          if (res.code === 20000) {
            _this.$message({
              type: "success",
              message: "删除成功!",
            });
            _this.getInfoList()
          }
        });

      },
      // 格式化表格数据
      tableFormat(row, column, index) {
        // console.log(row, column, index)
        let str = ''
        switch (column.property) {
          case 'update_time':
            str = row.update_time ? row.update_time.slice(0, 10) : ''
            break
          case 'content':
            str = row.content.slice(0, 20)
            break
          case 'create_time':
            str = row.create_time.slice(0, 10)
            break
          case 'status':
            str = row.status == 0 ? '正常' : '隐藏'
            break
        }
        return str
      },
      // 显示删除弹窗
      showDeleteForm(row) {
        this.$confirm('确定删除用户「' + row.nick_name + '」吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.removeInfo(row)
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除',
            duration: 500,
          })
        })
      },
    }
  };
</script>

<style>
  body .el-table tr:first-child th {
    font-size: 15px;
    color: black;
    border-bottom: 2px solid #999999;
  }
</style>

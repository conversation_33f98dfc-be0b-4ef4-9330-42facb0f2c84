<template>
  <div class="app-container">
    <div class="filter-container">
      <el-row>

        <el-input
          v-model="listQuery.key_word"
          placeholder="关键字"
          style="width: 200px;"
          class="filter-item"
        />
        <el-button

          class="filter-item"
          type="info"
          size="mini"
          style="width: 60px;"
          @click="getUserList"
        >搜索
        </el-button>
        <el-button
          class="filter-item"
          type="primary"
          size="mini"
          icon="el-icon-plus"
          style="float: right"
          @click="addUserForm"
        >添加用户
        </el-button>
      </el-row>
    </div>
    <el-table
      empty-text="-"
      v-loading="listLoading"
      :data="list"
      fit
      highlight-current-row
      style="width: 100%;">
      <el-table-column type="index" width="50"/>
      <el-table-column v-for="(ctl,index) in table_ctl" :formatter="ctl.formatter" :sortable="ctl.sortable" :key="index" :label="ctl.label" :prop="ctl.prop" v-if="ctl.visiable"
                       align="center"></el-table-column>

<!--      <el-table-column align="center" :formatter="tableFormat" v-if="fullPermission()" label="所属餐厅" prop="lot_id"/>-->
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200px">
        <template slot="header" slot-scope="scope">
          <span style="margin-right: 15px">操作</span>
          <el-tooltip class="item" effect="dark" :hide-after="500" content="设置列" placement="top">
            <el-button icon="el-icon-setting" @click="table_dialog_form_visiable = true" circle type="text"></el-button>
          </el-tooltip>
        </template>
        <template slot-scope="scope">
          <el-tooltip class="item" effect="dark" :hide-after="500" content="编辑" placement="top">
            <el-button icon="el-icon-edit" class="my-blue" circle size="medium" @click="editUserForm(scope.row)"></el-button>
          </el-tooltip>
          <el-tooltip class="item" effect="dark" :hide-after="500" content="删除" placement="top">
            <el-button icon="el-icon-delete" class="my-red" circle size="medium" @click="showDeleteForm(scope.row)"></el-button>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      style="width: 100%;"
      custom-class="dialogwidth">
      <el-form
        ref="dataForm"
        :model="form"
        label-position="left"
        label-width="150px">
        <div class="lb">
          <el-form-item label="昵称：">
            <el-input v-model="form.name"></el-input>
          </el-form-item>
          <el-form-item label="用户名：">
            <el-input v-model="form.username"></el-input>
          </el-form-item>
          <el-form-item label="密码：">
            <el-input v-model="form.password" type="password" show-password></el-input>
          </el-form-item>
          <el-form-item label="角色：">
            <el-select style="width: 100%;" v-model="form.roles" multiple>
              <el-option :label="i.name" :value="i.id" v-for="(i,index) in role_list" :key="index">
              </el-option>
            </el-select>
          </el-form-item>
<!--          <el-form-item v-if="fullPermission()" label="所属餐厅：">-->
<!--            <el-select style="width: 100%;" v-model="form.lot_id">-->
<!--              <el-option :label="i.name" :value="i.id" v-for="(i,index) in parkLotlist" :key="index">-->
<!--              </el-option>-->
<!--            </el-select>-->
<!--          </el-form-item>-->
          <el-form-item label="号码：">
            <el-input v-model="form.mobile"></el-input>
          </el-form-item>
          <el-form-item label="备注：">
            <el-input v-model="form.remark"></el-input>
          </el-form-item>
        </div>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="dialogStatus==='create'?addUser():editUser()">确定</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="表格列编辑"
      :visible.sync="table_dialog_form_visiable"
      style="width: 100%;"
      custom-class="dialogwidth">
      <el-form
        ref="dataForm"
        label-position="left"
        label-width="200px">
        <div class="lb">
          <el-form-item :label="ctl.label + '：'" v-for="(ctl,index) in table_ctl" :key="index">
            <el-switch v-model="ctl.visiable" active-text="可见" inactive-text="不可见" active-color="#13ce66" inactive-color="#666666"></el-switch>
          </el-form-item>
        </div>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="table_dialog_form_visiable = false" type='primary'>确定</el-button>
      </div>
    </el-dialog>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.page"
      :page-sizes="[20,50,99]"
      :page-size="99"
      :limit.sync="listQuery.limit"
      @pagination="getUserList"
    />
  </div>
</template>

<script>
  import Pagination from '@/components/Pagination/index' // secondary package based on el-pagination
  import { getSystemUser, addSystemUser, deleteSystemUser, editSystemUser, getRole } from '@/api/system'
  import Vue from 'vue'
  import { mapGetters } from 'vuex'
  import { getLot } from '@/api/manage'

  export default {
    components: { Pagination },
    data() {
      return {
        list: [],
        parkLotlist: [],
        total: 0,
        role_list: [],
        listLoading: false,
        form: {},
        dialogFormVisible: false,
        textMap: {
          edit: '编辑',
          create: '添加'
        },
        table_dialog_form_visiable: false,
        table_ctl: [
          {
            label: 'ID',
            prop: 'id',
            sortable: false,
            visiable: true,
            formatter: null
          },
          {
            label: '昵称',
            prop: 'name',
            sortable: false,
            visiable: true,
            formatter: null
          },
          {
            label: '用户名',
            prop: 'username',
            sortable: false,
            visiable: true,
            formatter: null
          },
          {
            label: '角色',
            prop: 'roles',
            sortable: false,
            visiable: true,
            formatter: this.tableFormat
          },
          {
            label: '号码',
            prop: 'mobile',
            sortable: false,
            visiable: true,
            formatter: null
          },
          {
            label: '备注',
            prop: 'remark',
            sortable: false,
            visiable: true,
            formatter: null
          },
          // {
          //   label: '更新时间',
          //   prop: 'update_time',
          //   sortable: false,
          //   visiable: true,
          //   formatter: this.tableFormat
          // },
          // {
          //   label: '创建时间',
          //   prop: 'create_time',
          //   sortable: false,
          //   visiable: true,
          //   formatter: this.tableFormat
          // }
        ],
        dialogStatus: '',
        listQuery: {
          page: 1,
          limit: 99,
          start_time: null,
          end_time: null
        }
      }
    },
    computed: {
      ...mapGetters([
        'roles'
      ])
    },
    created() {
      this.getUserList()
      this.getRoles()
    },
    mounted() {
    },
    methods: {
      //获取系统用户数据
      getUserList() {
        let _this = this
        _this.listLoading = true
        _this.list = []
        getSystemUser(_this.listQuery).then(response => {
          _this.list = response.items
          _this.total = response.totalCount
          _this.listLoading = false
        })
        setTimeout(() => {
          _this.listLoading = false
        }, 3.5 * 1000)
      },
      //获取角色数据
      getRoles() {
        var _this = this
        _this.role_list = []
        getRole().then((res) => {
          // _this.role_list = res.items
          if (_this.fullPermission()) {
            _this.role_list = res.items
            // _this.getParkLot()
          } else {
            res.items.forEach(ele => {
              if (ele.id !== 1 && ele.id !== 2) {
                _this.role_list.push(ele)
              }
            })
          }
        })
      },
      //获取停车场数据
      getParkLot() {
        let _this = this
        _this.parkLotlist = [{ id: 0, name: '无' }]
        getLot().then(response => {
          response.items.forEach(ele => {
            _this.parkLotlist.push(ele)
          })
        })
      },
      //是否超级管理员
      fullPermission() {
        let permission = false
        this.roles.forEach(element => {
          if (element.id === 1) {
            permission = true
          }
        })
        return permission
      },
      //添加系统用户
      addUser() {
        let _this = this
        let para = Object.assign({}, _this.form)
        addSystemUser(para).then((res) => {
          if (res.code === 20000) {
            _this.$message({
              type: 'success',
              message: res.message
            })
            _this.dialogFormVisible = false
            _this.getUserList()
          }
        })

      },
      //显示添加系统用户dialogue
      addUserForm() {
        this.resetForm()
        this.dialogStatus = 'create'
        this.dialogFormVisible = true
      },
      //修改添加系统用户dialogue
      editUserForm(row) {
        this.resetForm()
        this.form = row
        this.dialogStatus = 'edit'
        this.dialogFormVisible = true
      },
      //重置表单
      resetForm() {
        this.form = {}
      },
      //修改系统用户
      editUser() {
        let _this = this
        let para = Object.assign({}, _this.form)
        delete para.update_time
        editSystemUser(para).then((res) => {
          if (res.code === 20000) {
            _this.$message({
              type: 'success',
              message: res.message
            })
            _this.dialogFormVisible = false
            _this.getUserList()
          }
        })
      },
      // 删除用户
      removeUser(row) {
        let _this = this
        deleteSystemUser(row.id).then((res) => {
          if (res.code === 20000) {
            _this.$message({
              type: 'success',
              message: '删除成功!'
            })
            _this.getUserList()
          }
        })

      },
      // 格式化表格数据
      tableFormat(row, column, index) {
        // console.log(row, column, index)
        let str = ''
        switch (column.property) {
          case 'roles':
            let role_li = []
            row.roles.forEach(el => {
              this.role_list.forEach((ele, index) => {
                if (el === ele.id) {
                  role_li.push(ele.name)
                }
              })
            })
            str = role_li.join(',')
            break
          case 'lot_id':
            this.parkLotlist.forEach(el => {
              if(el.id === row.lot_id){
                str = el.name
              }
            })
            break
          case 'purpose':
            this.purposeList.forEach((ele, index) => {
              if (index === row.purpose) {
                str = ele
              }
            })
            break
          case 'update_time':
            str = row.update_time ? row.update_time.slice(0, 10) : ''
            break
          case 'create_time':
            str = row.create_time.slice(0, 10)
            break
        }
        return str
      },
      // 显示删除弹窗
      showDeleteForm(row) {
        this.$confirm('确定删除用户「' + row.name + '」吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.removeUser(row)
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除',
            duration: 500
          })
        })
      }
    }
  }
</script>
<style>
  body .el-table tr:first-child th {
    font-size: 15px;
    color: black;
    border-bottom: 2px solid #999999;
  }
</style>

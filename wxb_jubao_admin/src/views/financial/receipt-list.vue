<template>
  <div class="app-container">
    <div class="filter-container">
      <el-row>

        <el-input
          v-model="listQuery.key_word"
          placeholder="关键字"
          style="width: 200px;"
          class="filter-item"
        />
        <el-button

          class="filter-item"
          type="info"
          size="mini"
          style="width: 60px;"
          @click=""
        >搜索
        </el-button>
        <el-button
          class="filter-item"
          type="primary"
          size="mini"
          icon="el-icon-plus"
          style="float: right"
          @click="addForm"
        >添加发票
        </el-button>
      </el-row>
    </div>
            <el-table
              empty-text="-"
              v-loading="listLoading"
      :data="list"
      fit
      highlight-current-row
      style="width: 100%;">
      <el-table-column type="index" width="50"/>
      <el-table-column label="ID" prop="ID" align="center"/>
      <el-table-column label="发票编号" prop="CODE" align="center"/>
      <el-table-column label="商品" prop="GOODS" align="center"/>
      <el-table-column label="销货单位名称" prop="SELLER" align="center"/>
      <el-table-column label="销货单位地址" prop="SELLER_ADDRESS" align="center"/>
      <el-table-column label="采购单位名称" prop="BUYER" align="center"/>
      <el-table-column label="采购单位地址" prop="BUYER_ADDRESS" align="center"/>
      <el-table-column label="创建时间" prop="create_time" align="center"/>
      <el-table-column label="备注" prop="remark" align="center"/>

      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200px">
        <template slot-scope="scope">
          <el-button icon="el-icon-edit" class="my-blue" circle size="medium" @click="updateForm(scope.row)" type="primary"></el-button>
          <el-popover
            placement="top"
            v-if="!(scope.row.STATUS===3)"
            :ref="`popover-${scope.$index}`">
            <p>确定删除此行数据吗？</p>
            <div style="text-align: center; margin: 0">
              <el-button type="primary" size="mini"
                         @click="remove(scope.row),scope._self.$refs[`popover-${scope.$index}`].doClose()">
                确定
              </el-button>
              <el-button type="primary" size="mini"
                         @click="scope._self.$refs[`popover-${scope.$index}`].doClose()">
                取消
              </el-button>
            </div>
            <el-button icon="el-icon-delete" class="my-red" circle size="medium" slot="reference"></el-button>
          </el-popover>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      style="width: 100%;"
      custom-class="dialogwidth">
      <el-form
        ref="dataForm"
        :model="form"
        label-position="left"
        label-width="150px">
        <div class="lb">
          <el-form-item label="发票编号：">
            <el-input v-model="form.CODE" ></el-input>
          </el-form-item>
          <el-form-item label="商品：">
            <el-input v-model="form.GOODS" ></el-input>
          </el-form-item>
          <el-form-item label="数量：">
            <el-input v-model="form.COUNT" ></el-input>
          </el-form-item>
          <el-form-item label="单价：">
            <el-input v-model="form.PRICE" ></el-input>
          </el-form-item>
          <el-form-item label="总价：">
            <el-input v-model="form.FEE" ></el-input>
          </el-form-item>
          <el-form-item label="税额：">
            <el-input v-model="form.TEX" ></el-input>
          </el-form-item>
          <el-form-item label="税后合计：">
            <el-input v-model="form.FEE_WITH_TEX" ></el-input>
          </el-form-item>
          <el-form-item label="采购单位名称：">
            <el-input v-model="form.BUYER" ></el-input>
          </el-form-item>
          <el-form-item label="采购银行账户：">
            <el-input v-model="form.BUYER_BANK_NUM" ></el-input>
          </el-form-item>
          <el-form-item label="采购单位地址：">
            <el-input v-model="form.BUYER_ADDRESS" ></el-input>
          </el-form-item>
          <el-form-item label="销货单位名称：">
            <el-input v-model="form.SELLER" ></el-input>
          </el-form-item>
          <el-form-item label="销货银行账户：">
            <el-input v-model="form.SELLER_BANK_NUM" ></el-input>
          </el-form-item>
          <el-form-item label="销货单位地址：">
            <el-input v-model="form.SELLER_ADDRESS" ></el-input>
          </el-form-item>
          <el-form-item label="开票时间：">
            <el-date-picker v-model="form.RELEASE_TIME" value-format="yyyy-MM-dd"></el-date-picker>
          </el-form-item>
          <el-form-item label="备注：">
            <el-input v-model="form.remark"></el-input>
          </el-form-item>
        </div>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="dialogStatus==='create'?add():edit()">确定</el-button>
      </div>
    </el-dialog>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.page"
      :page-sizes="[20,50,99]"
      :page-size="99"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />
  </div>
</template>

<script>
  import Pagination from "@/components/Pagination/index"; // secondary package based on el-pagination
  import Vue from "vue";

  export default {
    components: {Pagination},
    data() {
      return {
        list:[
          {
            'ID':'1',
            'CODE':'********',
            'GOODS':'计算机',
            'COUNT':'1',
            'PRICE':'5999.00',
            'FEE':'5999.00',
            'TEX':'1019.83',
            'FEE_WITH_TEX':'7018.83',
            'SELLER':'A公司',
            'SELLER_BANK_NUM':'**************',
            'SELLER_ADDRESS':'北京市海淀区xx路12号',
            'BUYER':'采购单位',
            'BUYER_BANK_NUM':'**************',
            'BUYER_ADDRESS':'北京市海淀区xx路11号',
            'RELEASE_TIME':'2021-08-29 15:23:01',
            'create_time':'2021-08-29 15:43:01',
          },{
            'ID':'2',
            'CODE':'********',
            'GOODS':'计算机',
            'COUNT':'1',
            'PRICE':'5999.00',
            'FEE':'5999.00',
            'TEX':'1019.83',
            'FEE_WITH_TEX':'7018.83',
            'SELLER':'A公司',
            'SELLER_BANK_NUM':'**************',
            'SELLER_ADDRESS':'北京市海淀区xx路12号',
            'BUYER_ADDRESS':'北京市海淀区xx路11号',
            'BUYER':'采购单位',
            'BUYER_BANK_NUM':'**************',
            'RELEASE_TIME':'2021-08-29 15:23:01',
            'create_time':'2021-08-29 15:43:01',
          },{
            'ID':'3',
            'CODE':'********',
            'GOODS':'计算机',
            'COUNT':'1',
            'PRICE':'5999.00',
            'FEE':'5999.00',
            'TEX':'1019.83',
            'FEE_WITH_TEX':'7018.83',
            'SELLER':'A公司',
            'SELLER_BANK_NUM':'**************',
            'SELLER_ADDRESS':'北京市海淀区xx路12号',
            'BUYER_ADDRESS':'北京市海淀区xx路11号',
            'BUYER':'采购单位',
            'BUYER_BANK_NUM':'**************',
            'RELEASE_TIME':'2021-08-29 15:23:01',
            'create_time':'2021-08-29 15:43:01',
          },{
            'ID':'4',
            'CODE':'********',
            'GOODS':'计算机',
            'COUNT':'1',
            'PRICE':'5999.00',
            'FEE':'5999.00',
            'TEX':'1019.83',
            'FEE_WITH_TEX':'7018.83',
            'SELLER':'A公司',
            'SELLER_BANK_NUM':'**************',
            'SELLER_ADDRESS':'北京市海淀区xx路12号',
            'BUYER_ADDRESS':'北京市海淀区xx路11号',
            'BUYER':'采购单位',
            'BUYER_BANK_NUM':'**************',
            'RELEASE_TIME':'2021-08-29 15:23:01',
            'create_time':'2021-08-29 15:43:01',
          },
        ],
        total:0,
        listLoading:false,
        form:{},
        dialogFormVisible:false,
        textMap: {
          edit: '编辑记录',
          create: 'Create'
        },
        dialogStatus: '',
        listQuery: {
          page: 1,
          limit: 99,
          start_time: null,
          end_time: null,
        },
      };
    },
    created() {
      this.getList();
    },
    mounted() {
    },
    methods: {
      getList() {
        let _this = this
        _this.listLoading = true;
        // getMatchSettleList(this.listQuery).then(response => {
        //   this.list = response.items;
        //   this.total = response.total;
        // });

        setTimeout(()=>{
          _this.listLoading = false;
        },0.5*1000)
      },
      add(){
        let _this = this

      },
      addForm(){
        this.resetForm()
        this.dialogStatus = 'create'
        this.dialogFormVisible = true
      },
      updateForm(row){
        this.resetForm()
        this.form = row
        this.dialogStatus = 'edit'
        this.dialogFormVisible = true
      },
      resetForm(){
        this.form = {}
      },
      edit(){
        let _this = this
        _this.dialogFormVisible = false
      },
      remove(row){
        let _this = this
        const index = this.list.indexOf(row)
        this.list.splice(index, 1)

      },
    }
  };
</script>

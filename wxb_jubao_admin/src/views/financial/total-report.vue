<template>
  <div class="app-container">
    <div class="filter-container">
      <el-row>
        <el-date-picker
          v-model="dateQuery.year"
          type="year"
          clearable
          @change="dateChange"
          value-format="yyyy"
          placeholder="选择年份">
        </el-date-picker>
        <el-date-picker
          v-model="dateQuery.month"
          type="month"
          clearable
          value-format="yyyy-MM"
          @change="dateChange"
          placeholder="选择月份">
        </el-date-picker>
        <el-button

          class="filter-item"
          type="info"
          size="mini"
          style="width: 60px;"
          @click="getList"
        >统计
        </el-button>
      </el-row>
    </div>
    <el-table
      empty-text="-"
      class="el-table"
      v-loading="listLoading"
      :data="list"
      fit
      border
      highlight-current-row
      style="width: 100%;">
      <!--      <el-table-column type="index" width="50"/>-->
      <el-table-column label="付款方式" prop="name" align="center"/>
      <el-table-column label="总流量" prop="count" align="center"/>
      <el-table-column label="应收金额" prop="should_pay" align="center"/>
      <el-table-column label="实收金额" prop="already_paid" align="center"/>
    </el-table>
  </div>
</template>

<script>
  import Pagination from '@/components/Pagination/index' // secondary package based on el-pagination
  import { getFeeReport } from '@/api/report'
  import Vue from 'vue'

  export default {
    components: { Pagination },
    data() {
      return {
        list: [],
        total: 0,
        listLoading: false,
        form: {},
        dialogFormVisible: false,
        textMap: {
          edit: '编辑记录',
          create: 'Create'
        },
        dateQuery: {month: new Date().getFullYear() + '-' + (new Date().getMonth() + 1)},
        dialogStatus: '',
        listQuery: {}
      }
    },
    created() {
      this.getList()
    },
    mounted() {
    },
    methods: {
      //获取数据
      getList() {
        let _this = this
        _this.listLoading = true
        let para = Object.assign({}, _this.listQuery)
        if (_this.dateQuery.month) {
          let date = _this.dateQuery.month.split('-')
          para.start_date = _this.dateQuery.month + '-01'
          para.end_date = _this.getMonthFinalDay(date[0], date[1])
        } else if (_this.dateQuery.year) {
          para.start_date = _this.dateQuery.year + '-01-01'
          para.end_date = _this.getMonthFinalDay(_this.dateQuery.year, 12)
        }
        getFeeReport(para).then(response => {
          _this.listLoading = false
          _this.list = []
          for (var i in response.reports){
            _this.list.push(response.reports[i])
          }
        });

        setTimeout(() => {
          _this.listLoading = false
        }, 5 * 1000)
      },
      //设置月份/年份
      dateChange(evt) {
        if (evt.length > 4) {
          this.dateQuery = {
            month: evt
          }
        } else {
          this.dateQuery = {
            year: evt
          }
        }
      },
      //获取月份最后一天
      getMonthFinalDay(year, month) {
        var day = '';
        if (year == null || year == undefined || year == '') {
          year = new Date().getFullYear();
        }
        if (month == null || month == undefined || month == '') {
          month = new Date().getMonth() + 1;
        }
        day = new Date(new Date(year, month).setDate(0)).getDate();
        return year + "-" + month + "-" + day;
      },
    }
  }
</script>

<style>
  body .el-table tr:first-child th{
    font-size: 15px;
    color: black;
    border-bottom: 2px solid #999999;

  }
</style>

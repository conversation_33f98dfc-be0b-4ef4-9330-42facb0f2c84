<template>
  <div class="app-container">
    <div class="filter-container">
      <el-row>
        <el-date-picker
          v-model="dateQuery.year"
          type="year"
          clearable
          @change="dateChange"
          value-format="yyyy"
          placeholder="选择年份">
        </el-date-picker>
        <el-date-picker
          v-model="dateQuery.month"
          type="month"
          clearable
          value-format="yyyy-MM"
          @change="dateChange"
          placeholder="选择月份">
        </el-date-picker>
        <el-input v-model="listQuery.start_slot" style="width: 100px;" placeholder="免费时段"
                  @input="listQuery.start_slot=listQuery.start_slot.replace(/[^\d]/g,'')">

        </el-input>
        <el-input v-model="listQuery.unit" style="width: 100px;" placeholder="单位时间"
                  @input="listQuery.unit=listQuery.unit.replace(/[^\d]/g,'')">

        </el-input>
        <el-input v-model="listQuery.max_unit" style="width: 100px;" placeholder="时间上限"
                  @input="listQuery.max_unit=listQuery.max_unit.replace(/[^\d]/g,'')">

        </el-input>
        <el-button

          class="filter-item"
          type="info"
          size="mini"
          style="width: 60px;"
          @click="getList"
        >统计
        </el-button>
      </el-row>
    </div>
            <el-table
              empty-text="-"
              v-loading="listLoading"
      :data="list"
              class="el-table"
      fit
      style="width: 100%;">
<!--      <el-table-column type="index" width="50"/>-->
      <el-table-column label="停车时间" prop="parking_time" align="center"/>

      <el-table-column label="临时停车" align="center">
        <el-table-column label="总流量" prop="total_flow_temp" align="center"/>
        <el-table-column label="应收金额" prop="should_charge_temp" align="center"/>
        <el-table-column label="实收金额" prop="charge_temp" align="center"/>
      </el-table-column>

      <el-table-column label="月租车" align="center">
        <el-table-column label="总流量" prop="total_flow_pack" align="center"/>
        <el-table-column label="应收金额" prop="should_charge_pack" align="center"/>
        <el-table-column label="实收金额" prop="charge_pack" align="center"/>
      </el-table-column>

    </el-table>


  </div>
</template>

<script>
  import Pagination from "@/components/Pagination/index"; // secondary package based on el-pagination
  import {getFlowReport} from '@/api/report'
  import Vue from "vue";

  export default {
    components: {Pagination},
    data() {
      return {
        list: [
        ],
        total: 0,
        listLoading: false,
        form: {},
        dateQuery: {month: new Date().getFullYear() + '-' + (new Date().getMonth() + 1)},
        dialogFormVisible: false,
        textMap: {
          edit: '编辑记录',
          create: 'Create'
        },
        dialogStatus: '',
        listQuery: {
          start_slot: 30,
          unit: 60,
          max_unit: 7,
        },
      };
    },
    created() {
      this.getList();
    },
    mounted() {
    },
    methods: {
      //获取数据
      getList() {
        let _this = this
        _this.listLoading = true;
        let para = Object.assign({}, _this.listQuery)
        if (_this.dateQuery.month) {
          let date = _this.dateQuery.month.split('-')
          para.start_date = _this.dateQuery.month + '-01'
          para.end_date = _this.getMonthFinalDay(date[0], date[1])
        } else if (_this.dateQuery.year) {
          para.start_date = _this.dateQuery.year + '-01-01'
          para.end_date = _this.getMonthFinalDay(_this.dateQuery.year, 12)
        }
        getFlowReport(para).then(response => {
          _this.list = response.reports;
          response.total_report.parking_time = '总计'
          _this.list.push(response.total_report)
          // _this.total = response.total;
        });
        setTimeout(() => {
          _this.listLoading = false;
        }, 0.5 * 1000)
      },
      //设置月份/年份
      dateChange(evt) {
        if (evt.length > 4) {
          this.dateQuery = {
            month: evt
          }
        } else {
          this.dateQuery = {
            year: evt
          }
        }
      },
      //获取月份最后一天
      getMonthFinalDay(year, month) {
        var day = '';
        if (year == null || year == undefined || year == '') {
          year = new Date().getFullYear();
        }
        if (month == null || month == undefined || month == '') {
          month = new Date().getMonth() + 1;
        }
        day = new Date(new Date(year, month).setDate(0)).getDate();
        return year + "-" + month + "-" + day;
      },
    }
  };
</script>

<style>
  body .el-table tr:first-child th{
    font-size: 15px;
    color: black;
    border-bottom: 2px solid #999999;

  }
</style>

<template>
  <div class="app-container">
    <div class="filter-container">
      <el-row>
        <el-col :span="4">
          <el-input
            v-model="listQuery.filter.name"
            placeholder="业主姓名"
            style="width: 90%;margin-left: 5%;"
            class="filter-item"
            clearable
          />
        </el-col>
        <el-col :span="4">
          <el-input
            v-model="listQuery.filter.phone"
            placeholder="电话号码"
            style="width: 90%;margin-left: 5%;"
            class="filter-item"
            clearable
          />
        </el-col>
        <el-col :span="4">
          <el-input
            v-model="listQuery.plates"
            placeholder="车牌"
            style="width: 90%;margin-left: 5%;"
            class="filter-item"
            clearable
          />
        </el-col>
        <el-col :span="4">
          <el-button
            class="filter-item"
            size="mini"
            type="warning"
            style="width: 60px;"
            @click="getHouseHolderList"
          >搜索
          </el-button>
          <el-button
            class="filter-item"
            type="info"
            size="mini"
            style="width: 60px;"
            @click="resetFilter"
          >重置
          </el-button>
        </el-col>
        <el-button
          class="filter-item"
          type="primary"
          size="mini"
          icon="el-icon-plus"
          style="float: right"
          @click="addHouseHolderForm"
        >添加业主
        </el-button>
          <el-button
            class="filter-item"
            type="info"
            icon="el-icon-download"
            style="float: right;"
            size="mini"
            @click="exportTable"
          >导出报表
          </el-button>
        <!--        <em style="color: orange">包月单价：{{user_monthly.config_value}}</em>-->
      </el-row>
    </div>
    <el-table
      empty-text="-"
      v-loading="listLoading"
      :data="list"
      fit
      highlight-current-row
      style="width: 100%;">
      <el-table-column type="index" width="50"/>
      <el-table-column v-for="(ctl,index) in table_ctl" :formatter="ctl.formatter" :sortable="ctl.sortable" :key="index" :label="ctl.label" :prop="ctl.prop" v-if="ctl.visiable && index < 6 "
                       align="center"></el-table-column>

      <el-table-column label="状态" prop="valid" align="center">
        <template slot-scope="scope">
          <span :style="scope.row.valid?'color:limegreen;':'color:grey;'">{{scope.row.valid?' 启用':' 禁用'}}</span>
        </template>
      </el-table-column>

      <el-table-column v-for="(ctl,index) in table_ctl" :formatter="ctl.formatter" :sortable="ctl.sortable" :key="index" :label="ctl.label" :prop="ctl.prop" v-if="ctl.visiable && index > 5"
                       align="center"></el-table-column>

      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200px">
        <template slot="header" slot-scope="scope">
          <span style="margin-right: 15px">操作</span>
          <el-tooltip class="item" effect="dark" :hide-after="500" content="设置列" placement="top">
            <el-button icon="el-icon-setting" @click="table_dialog_form_visiable = true" circle type="text"></el-button>
          </el-tooltip>
        </template>
        <template slot-scope="scope">
          <el-tooltip class="item" effect="dark" :hide-after="500" content="车位管理" placement="top">
            <el-button icon="el-icon-s-order" class="my-green" circle size="medium" @click="updateHouseHolderSeatForm(scope.row)"></el-button>
          </el-tooltip>
          <el-tooltip class="item" effect="dark" :hide-after="500" content="编辑" placement="top">
            <el-button icon="el-icon-edit" class="my-blue" circle size="medium" @click="updateHouseHolderForm(scope.row)"></el-button>
          </el-tooltip>
<!--          <el-tooltip class="item" effect="dark" :hide-after="500" content="充值" placement="top">-->
<!--            <el-button circle size="medium" @click="rechargeHouseHolderForm(scope.row)" class="my-green">￥</el-button>-->
<!--          </el-tooltip>-->
          <el-tooltip class="item" effect="dark" :hide-after="500" content="删除" placement="top">
            <el-button icon="el-icon-delete" class="my-red" circle size="medium" @click="showDeleteForm(scope.row)"></el-button>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      style="width: 100%;"
      custom-class="dialogwidth">
      <el-form
        ref="dataForm"
        :model="form"
        label-position="left"
        label-width="150px">
        <div class="lb">
          <el-form-item label="业主姓名：">
            <el-input v-model="form.name"></el-input>
          </el-form-item>
          <el-form-item label="手机号码：">
            <el-input v-model="form.phone"></el-input>
          </el-form-item>
          <el-form-item label="生效期日：">
            <el-date-picker v-model="form.month_package_valid_from" value-format="yyyy-MM-dd" style="width: 100%;" placeholder="生效期日"></el-date-picker>
          </el-form-item>
          <el-form-item label="失效日期：">
            <el-date-picker v-model="form.month_package_valid_to" value-format="yyyy-MM-dd" style="width: 100%;" placeholder="失效日期"></el-date-picker>
          </el-form-item>
          <el-form-item label="家庭地址：">
            <el-input v-model="form.address"></el-input>
          </el-form-item>
          <el-form-item label="状态：">
            <el-switch v-model="form.valid"></el-switch>
          </el-form-item>
          <el-form-item label="车牌：">
            <el-button v-if="form.plates && form.plates.length===0" icon="el-icon-plus" circle type="primary" @click="form.plates.push('')"></el-button>
            <el-row v-for="(plate,index) in form.plates" :key="index" style="margin-bottom: 10px;">
              <el-input style="width: 60%" v-model="form.plates[index]" @input="plateInput(index)"></el-input>
              <el-button icon="el-icon-minus" circle type="danger" @click="form.plates.splice(index,1)" v-if="index!==0"></el-button>
              <el-button icon="el-icon-plus" circle type="primary" @click="form.plates.push('')" v-if="index===0"></el-button>
            </el-row>
          </el-form-item>
        </div>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="dialogStatus==='create'?addHouseHolder():editHouseHolder()">确定</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="用户充值"
      :visible.sync="rechargeFormVisible"
      style="width: 100%;"
      custom-class="dialogwidth">
      <el-form
        ref="dataForm"
        :model="rechargeForm"
        label-position="left"
        label-width="150px">
        <div class="lb">
          <el-form-item label="用户ID：">
            <el-input v-model="form.id" readonly></el-input>
          </el-form-item>
          <el-form-item label="姓名：">
            <el-input v-model="form.name" readonly></el-input>
          </el-form-item>
          <el-form-item label="车位ID：">
            <el-input v-if="form.charging" v-model="form.charging.id" readonly></el-input>
          </el-form-item>
          <el-form-item label="车位名称：">
            <el-input v-if="form.charging" v-model="form.charging.name" readonly></el-input>
          </el-form-item>
          <el-form-item label="车位到期时间：">
            <el-input v-if="form.charging" v-model="form.charging.month_package_valid_to" readonly></el-input>
          </el-form-item>
          <el-form-item label="充值数量(月)：">
            <el-input v-model="rechargeForm.valid_months" style="width: 100%;"
                      @input="rechargeForm.valid_months = rechargeForm.valid_months.replace(/\D/g,''),packageTypeSelect(rechargeForm.valid_months)"></el-input>
          </el-form-item>
          <el-form-item label="充值单价：">
            <el-col :span="18">
              <el-input v-model="user_monthly_tmp.config_value"
                        @input="user_monthly_tmp.config_value=user_monthly_tmp.config_value.replace(/\D/g,''),packageTypeSelect(rechargeForm.valid_months)"></el-input>
            </el-col>
            <el-col :span="6">
              <el-button type="primary" style="float: right" @click="packageTypeSelect(rechargeForm.valid_months,true)">重置</el-button>
            </el-col>
          </el-form-item>
          <el-form-item label="充值总额：">
            <el-input v-model="total_price" @input="total_price = total_price.replace(/\D/g,'')"></el-input>
          </el-form-item>
          <el-form-item label="备注：">
            <el-input v-model="rechargeForm.remark"></el-input>
          </el-form-item>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="rechargeFormVisible = false">取消</el-button>
        <el-button type="primary" @click="addHouseHolderRecharge">确定</el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="车位管理"
      :visible.sync="seatFormVisible"
      style="width: 100%;"
      custom-class="dialogwidth">
      <el-form
        ref="dataForm"
        :model="form"
        label-position="left"
        label-width="150px">
        <div class="el-row--flex" style="">
          <el-button v-if="form.seats && form.seats.length===0" icon="el-icon-plus" size="mini" circle type="primary" @click="addHouseHolderSeat"></el-button></div>
        <div>
          <el-form-item :label="'id：' + seat.id" v-if="form.seats" v-for="(seat,index) in form.seats" :key="index">
            <el-row style="margin-bottom: 10px;">
              <el-input v-model="seat.name" placeholder="车位名称" style="width: 20%"></el-input>
              <el-date-picker value-format="yyyy-MM-dd" placeholder="到期时间" style="width: 20%" v-model="seat.month_package_valid_to">aa</el-date-picker>
              <el-input v-model="seat.in_use_plate" placeholder="使用中的车牌" style="width: 20%" readonly></el-input>
              <el-tooltip class="item" effect="dark" :hide-after="2000" content="保存" placement="top">
                <el-button icon="el-icon-check" circle class="my-green" size="mini" @click="editHouseHolderSeat(seat,form)"></el-button>
              </el-tooltip>
<!--              <el-tooltip class="item" effect="dark" :hide-after="2000" :content="seat.valid ? '启用':'禁用'" placement="top">-->
<!--                <el-switch active-color="#13ce66" inactive-color="grey" v-if="seat.id" v-model="seat.valid"></el-switch>-->
<!--              </el-tooltip>-->
              <el-tooltip class="item" effect="dark" :hide-after="2000" :content="seat.in_use ? '已停车':'未停车'" placement="top">
                <el-switch disabled active-color="#13ce66" inactive-color="grey" v-if="seat.id" v-model="seat.in_use"></el-switch>
              </el-tooltip>
              <el-tooltip class="item" effect="dark" :hide-after="500" content="充值" placement="top">
                <el-button circle size="mini" @click="rechargeHouseHolderForm(form,seat)" class="my-blue">￥</el-button>
              </el-tooltip>
              <el-tooltip class="item" effect="dark" :hide-after="500" content="删除" placement="top">
              <el-button icon="el-icon-minus" circle class="my-red" size="mini" @click="showSeatDeleteForm(seat)"></el-button>
              </el-tooltip>
              <el-tooltip class="item" effect="dark" :hide-after="500" content="添加" placement="top">
              <el-button icon="el-icon-plus" circle  class="my-grey" size="mini" @click="addHouseHolderSeat" v-if="index===0"></el-button>
              </el-tooltip>
            </el-row>
          </el-form-item>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
<!--        <el-button @click="seatFormVisible = false">取消</el-button>-->
        <el-button @click="seatFormVisible = false" type="info">关闭</el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="表格列编辑"
      :visible.sync="table_dialog_form_visiable"
      style="width: 100%;"
      custom-class="dialogwidth">
      <el-form
        ref="dataForm"
        label-position="left"
        label-width="200px">
        <div class="lb">
          <el-form-item :label="ctl.label + '：'" v-for="(ctl,index) in table_ctl" :key="index">
            <el-switch v-model="ctl.visiable" active-text="可见" inactive-text="不可见" active-color="#13ce66" inactive-color="#666666"></el-switch>
          </el-form-item>
        </div>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="table_dialog_form_visiable = false" type='primary'>确定</el-button>
      </div>
    </el-dialog>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.page"
      :page-sizes="[20,50,99]"
      :page-size="99"
      :limit.sync="listQuery.limit"
      @pagination="getHouseHolderList"
    />
  </div>
</template>

<script>
  import Pagination from '@/components/Pagination/index' // secondary package based on el-pagination
  import Vue from 'vue'
  import {
    getHouseHolder,
    addHouseHolder,
    editHouseHolder,
    removeHouseHolder,
    getHouseHolderDetail,
    addAppUserPackage,
    addHouseHolderSeat,
    editHouseHolderSeat,
    removeHouseHolderSeat,
  } from '@/api/user'
  import { getSysConfig } from '@/api/system'
  import { mapGetters } from 'vuex'

  export default {
    components: { Pagination },
    data() {
      return {
        list: [],
        total: 0,
        rechargeForm: {},
        user_monthly: {},
        user_monthly_tmp: {},
        total_price: 0,
        table_dialog_form_visiable: false,
        table_ctl: [
          {
            label: 'ID',
            prop: 'id',
            sortable: false,
            visiable: true,
            formatter: null
          },
          {
            label: '车牌号',
            prop: 'plates[0]',
            sortable: false,
            visiable: true,
            formatter: null
          },
          {
            label: '电话',
            prop: 'phone',
            sortable: false,
            visiable: true,
            formatter: null
          },
          {
            label: '生效日期',
            prop: 'month_package_valid_from',
            sortable: false,
            visiable: true
            // formatter: this.tableFormat
          },
          {
            label: '失效日期',
            prop: 'month_package_valid_to',
            sortable: false,
            visiable: true,
            formatter: this.tableFormat
          },
          {
            label: '剩余时间',
            prop: 'left_days',
            sortable: false,
            visiable: true
            // formatter: this.tableFormat
          },
          {
            label: '发行人',
            prop: 'publisher',
            sortable: false,
            visiable: true,
            formatter: null
          },
          {
            label: '更新时间',
            prop: 'update_time',
            sortable: false,
            visiable: true,
            formatter: this.tableFormat
          },
          {
            label: '创建时间',
            prop: 'create_time',
            sortable: false,
            visiable: true,
            formatter: this.tableFormat
          }
          // {
          //   label: '备注',
          //   prop: 'remark',
          //   sortable: false,
          //   visiable: true,
          //   formatter: null
          // },
        ],
        package_type_list: [
          { value: 1, label: '1个月' }, { value: 3, label: '3个月' }, { value: 6, label: '6个月' }, { value: 12, label: '12个月' }],
        rechargeFormVisible: false,
        role_list: ['商家', '管理员'],
        listLoading: false,
        form: { plates: [] },
        dialogFormVisible: false,
        seatFormVisible:false,
        textMap: {
          edit: '编辑业主',
          create: '添加业主'
        },
        dialogStatus: '',
        listQuery: {
          page: 1,
          limit: 99,
          start_time: null,
          end_time: null,
          filter: {}
        }
      }
    },
    computed: {
      ...mapGetters([
        'user_info'
      ])
    },
    created() {
      this.getHouseHolderList()
      this.getConfig()
    },
    mounted() {
    },
    methods: {
      //获取业主数据
      getHouseHolderList() {
        let _this = this
        _this.listLoading = true
        let para = Object.assign({}, _this.listQuery)
        para = _this.clearObj(para)
        getHouseHolder(para).then(response => {
          _this.list = response.items
          _this.total = response.totalCount
          _this.listLoading = false
        })
      },
      //获取业主数据
      getHouseHolder(id) {
        let _this = this
        getHouseHolderDetail(id).then(response => {
          _this.form = response.items
        })
      },
      //获取收费标准
      getConfig() {
        let _this = this
        getSysConfig({ filter: { config_class: 'user_monthly', lot_id: _this.user_info.lot_id } }).then(response => {
          response.items.forEach(ele => {
            switch (ele.config_key) {
              case 'custom_month_pack_price':
                _this.user_monthly = ele
                _this.changeTitle(['包月单价：' + ele.config_value])
                break
            }
          })
        })
      },
      //添加业主
      addHouseHolder() {
        let _this = this
        let para = Object.assign({}, _this.form)
        para.plates.forEach(ele => {
          let count = 0
          para.plates.forEach((el, index) => {
            if (el === ele) {
              count++
              if (count > 1) {
                delete para.plates[index]
              }
            }
          })
        })
        // para.roles = [para.roles]
        addHouseHolder(para).then((res) => {
          if (res.code === 20000) {
            _this.$message({
              type: 'success',
              message: res.message
            })
            _this.dialogFormVisible = false
            _this.getHouseHolderList()
          }
        })
      },
      //添加业主车位
      addHouseHolderSeat() {
        let _this = this
        let para =
          {holder_id:_this.form.id}
        // para.roles = [para.roles]
        addHouseHolderSeat(para).then((res) => {
          if (res.code === 20000) {
            _this.form.seats.push(res.item)
            _this.$message({
              type: 'success',
              message: res.message
            })
            _this.getHouseHolderList()
          }
        })
      },
      //设置充值总价
      packageTypeSelect(evt, reset) {
        if (reset) {
          this.user_monthly_tmp = Object.assign({}, this.user_monthly)
        }
        // this.rechargeForm.valid_months = evt.replace(/[^\d]/g, '')
        this.total_price = parseInt(this.user_monthly_tmp.config_value) * evt
        // switch (evt) {
        //   case 0:
        //     this.total_price = parseInt(this.user_monthly.config_value)
        //     break;
        //   case 1:
        //     this.total_price = parseInt(this.user_monthly.config_value) * 3
        //     break;
        //   case 2:
        //     this.total_price = parseInt(this.user_monthly.config_value) * 6
        //     break;
        //   case 3:
        //     this.total_price = parseInt(this.user_monthly.config_value) * 12
        //     break;
        // }
      },
      //显示添加业主dialogue
      addHouseHolderForm() {
        this.resetForm()
        this.dialogStatus = 'create'
        this.dialogFormVisible = true
      },
      //修改添加业主dialogue
      updateHouseHolderForm(row) {
        this.resetForm()
        this.form = row
        this.dialogStatus = 'edit'
        this.dialogFormVisible = true
      },
      //修改添加业主dialogue
      updateHouseHolderSeatForm(row) {
        this.resetForm()
        this.form = row
        this.seatFormVisible = true
      },
      //充值表单
      resetForm() {
        this.form = { plates: [''], valid: true, lot_count: '1' }
        this.rechargeForm = { valid_months: 1 }
      },
      //显示业主充值dialogue
      rechargeHouseHolderForm(row,seat) {
        // this.resetForm()
        this.form.charging = seat
        this.total_price = parseInt(this.user_monthly.config_value)
        this.user_monthly_tmp = Object.assign({}, this.user_monthly)
        this.rechargeFormVisible = true
      },
      //修改业主
      editHouseHolder() {
        let _this = this
        let para = Object.assign({}, _this.form)
        para.plates.forEach(ele => {
          let count = 0
          para.plates.forEach((el, index) => {
            if (el === ele) {
              count++
              if (count > 1) {
                delete para.plates[index]
              }
            }
          })
        })
        // para.roles = [para.roles]
        editHouseHolder(para).then((res) => {
          if (res.code === 20000) {
            _this.$message({
              type: 'success',
              message: res.message
            })
            _this.dialogFormVisible = false
            _this.getHouseHolderList()
          }
        })
      },
      //修改业主车位
      editHouseHolderSeat(seat,form) {
        let _this = this
        let para = {
          id:seat.id,
          valid:seat.valid,
          name:seat.name,
        }
        seat.month_package_valid_to ? para.month_package_valid_to = seat.month_package_valid_to:''
        editHouseHolderSeat(para).then((res) => {
          if (res.code === 20000) {
            _this.$message({
              type: 'success',
              message: res.message
            })
            _this.getHouseHolder(form.id)
          }
        })
      },
      //输入车牌检测
      plateInput(index) {
        this.form.plates[index] = this.form.plates[index].replace(/[^\a-\z\A-\Z0-9\u4E00-\u9FA5]/g, '')
        let para = Object.assign({}, this.form)
        para.plates.forEach((ele, index) => {
          let count = 0
          para.plates.forEach((el, inde) => {
            if (el === ele) {
              count++
              if (count > 1 && inde > index) {
                this.$message({
                  type: 'warning',
                  duration: 1000,
                  message: '与第' + (index + 1) + '个车牌号相同'
                })
              }
            }
          })
        })
      },
      //业主充值
      addHouseHolderRecharge() {
        let _this = this
        if (!_this.total_price) {
          return
        }
        let para = {
          seat_id: _this.form.charging.id,
          valid_months: parseInt(_this.rechargeForm.valid_months),
          charge_price: parseInt(_this.user_monthly_tmp.config_value),
          charge_total: _this.total_price,
          remark: _this.rechargeForm.remark
        }
        para.user_id = _this.form.id
        // para.roles = [para.roles]
        addAppUserPackage(para).then((res) => {
          if (res.code === 20000) {
            _this.$message({
              type: 'success',
              message: res.message
            })
            _this.rechargeFormVisible = false
            _this.getHouseHolder(_this.form.id)

          }
        })

      },
      //删除业主
      removeHouseHolder(row) {
        let _this = this
        removeHouseHolder(row.id).then((res) => {
          if (res.code === 20000) {
            _this.$message({
              type: 'success',
              message: '删除成功!'
            })
            _this.getHouseHolderList()
          }
        })
      },
      //删除业主车位
      removeHouseHolderSeat(row) {
        let _this = this
        removeHouseHolderSeat(row.id).then((res) => {
          if (res.code === 20000) {
            _this.$message({
              type: 'success',
              message: '删除成功!'
            })
            // _this.form.seats.splice(_this.form.plates.indexOf(row),1)
            _this.getHouseHolder(_this.form.id)
            _this.getHouseHolderList()
          }
        })
      },
      // 显示删除弹窗
      showDeleteForm(row) {
        this.$confirm('确定删除用户「' + row.name + '」吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.removeHouseHolder(row)
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除',
            duration: 500
          })
        })
      },
      // 显示删除车位弹窗
      showSeatDeleteForm(row) {
        this.$confirm('确定id为「' + row.id + '」的车位吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.removeHouseHolderSeat(row)
        }).catch(() => {
        })
      },
      // 格式化表格数据
      tableFormat(row, column, index) {
        // console.log(row, column, index)
        let str = ''
        switch (column.property) {
          case 'update_time':
            str = row.update_time ? row.update_time.slice(0, 10) : ''
            break
          case 'month_package_valid_to':
            str = row.month_package_valid_to ? row.month_package_valid_to.slice(0, 10) : ''
            break
          case 'create_time':
            str = row.create_time.slice(0, 10)
            break

        }
        return str
      },
      //修改标题价格显示
      changeTitle(arr) {
        this.$store
          .dispatch('user/resetPrice', arr)
          .then(() => {
          })
          .catch(() => {
          })
      },
      // 重置表单数据
      resetFilter() {
        this.listQuery = {
          page: 1,
          limit: 99,
          filter: { package_remain_times: {} }
        }
        this.getHouseHolderList()
      },
      //去掉无用的数据
      clearObj(obj) {
        for (var ob in obj) {
          if (obj[ob] == null || obj[ob] === '') {
            delete obj[ob]
          } else if (typeof obj[ob] === 'object') {
            obj[ob] = this.clearObj(obj[ob])
          }
        }
        return obj
      },
      //导出表格
      exportTable() {
        let _this = this
        let para = _this.clearObj(Object.assign({}, this.listQuery))
        // let para = {}
        delete para.limit
        delete para.page
        para.export = 1
        // exportFile('business')
        getHouseHolder(para).then(res => {
          // console.log(res)
          if (res.data.code === 50001) {
            _this.$message({
              type: 'error',
              message: res.data.message,
              duration: 2500
            })
            return
          }
          if (res.data.type) {
            // 文件下载
            const blob = new Blob([res.data], {
              type: "application/vnd.ms-excel"
            });
            let link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.setAttribute('download', '导出文件.xlsx');
            link.click();
            link = null;
          } else {
            _this.$message({
              type: 'error',
              message: '导出出错',
              duration: 2500
            })
            // 返回json
          }
        })

      },
    }
  }
</script>
<style>
  .red-dot {
    background-color: red;
    height: 20px;
    width: 20px;
  }

  .green-dot {
    background-color: green;
    height: 20px;
    width: 20px;
  }

  body .el-table tr:first-child th {
    font-size: 15px;
    color: black;
    border-bottom: 2px solid #999999;
  }
</style>

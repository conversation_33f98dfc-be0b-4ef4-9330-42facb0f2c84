<template>
  <div class="app-container">
    <div class="filter-container">
      <el-row>

        <el-input
          v-model="listQuery.key_word"
          placeholder="关键字"
          style="width: 200px;"
          clearable
          class="filter-item"
        />
        <el-button

          class="filter-item"
          type="info"
          size="mini"
          style="width: 60px;"
          @click="getAppUserList"
        >搜索
        </el-button>
        <input
          ref="excel-upload-input"
          class="excel-upload-input"
          type="file"
          accept=".xlsx, .xls"
          style="display: none"
          @change="handleClick"
        />
<!--        <el-button-->
<!--          class="filter-item"-->
<!--          type="primary"-->
<!--          icon="el-icon-download"-->
<!--          style="float: right"-->
<!--          size="mini"-->
<!--          @click="exportTable"-->
<!--        >导出报表-->
<!--        </el-button>-->
      </el-row>
    </div>
            <el-table
              empty-text="-"
              v-loading="listLoading"
      :data="list"
      fit
      highlight-current-row
      style="width: 100%;">
      <el-table-column type="index" width="50"/>
      <el-table-column v-for="(ctl,index) in table_ctl" :formatter="ctl.formatter" :sortable="ctl.sortable" :key="index" :label="ctl.label" :prop="ctl.prop" v-if="ctl.visiable" align="center"></el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200px">
        <template slot="header" slot-scope="scope">
          <span style="margin-right: 15px">操作</span>
          <el-tooltip class="item" effect="dark" :hide-after="500" content="设置列" placement="top">
            <el-button icon="el-icon-setting" @click="table_dialog_form_visiable = true" circle type="text"></el-button>
          </el-tooltip>
        </template>
        <template slot-scope="scope">
          <el-tooltip class="item" effect="dark" :hide-after="500" content="编辑" placement="top">
            <el-button icon="el-icon-edit" class="my-blue" circle size="medium" @click="updateAppUserForm(scope.row)"></el-button>
          </el-tooltip>
<!--          <el-tooltip class="item" effect="dark" :hide-after="500" content="删除" placement="top">-->
<!--            <el-button icon="el-icon-delete" class="my-red" circle size="medium" @click="showDeleteForm(scope.row)"></el-button>-->
<!--          </el-tooltip>-->
        </template>
      </el-table-column>

    </el-table>

    <el-dialog
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      style="width: 100%;"
      custom-class="dialogwidth">
      <el-form
        ref="dataForm"
        :model="form"
        label-position="left"
        label-width="150px">
        <div class="lb">
          <el-form-item label="姓名：">
            <el-input v-model="form.realname" ></el-input>
          </el-form-item>
          <el-form-item label="用户名：">
            <el-input v-model="form.username" ></el-input>
          </el-form-item>
          <el-form-item label="电话号码：">
            <el-input v-model="form.telephone" ></el-input>
          </el-form-item>
          <el-form-item label="电子邮件：">
            <el-input v-model="form.email" ></el-input>
          </el-form-item>
          <el-form-item label="是否特殊用户：">
            <el-switch v-model="form.is_special" :active-value="1" :inactive-value="0"></el-switch>
          </el-form-item>
<!--          <el-form-item label="是否工作人员：">-->
<!--            <el-switch v-model="form.is_manager" ></el-switch>-->
<!--          </el-form-item>-->
<!--          <el-form-item label="禁用状态：">-->
<!--            <el-switch v-model="form.is_block" ></el-switch>-->
<!--          </el-form-item>-->
<!--          <el-form-item label="启用：">-->
<!--            <el-switch v-model="form.valid"></el-switch>-->
<!--          </el-form-item>-->
        </div>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="dialogStatus==='create'?addAppUser():editAppUser()">确定</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="用户充值"
      :visible.sync="rechargeFormVisible"
      style="width: 100%;"
      custom-class="dialogwidth">
      <el-form
        ref="dataForm"
        :model="rechargeForm"
        label-position="left"
        label-width="150px">
        <div class="lb">
          <el-form-item label="用户ID：">
            <el-input v-model="form.id" readonly></el-input>
          </el-form-item>
          <el-form-item label="昵称：">
            <el-input v-model="form.nick_name" readonly></el-input>
          </el-form-item>
          <el-form-item label="充值类型：">
            <el-select @change="packageTypeSelect" style="width: 100%;" v-model="rechargeForm.valid_months">
              <el-option :label="i.label" :value="i.value" v-for="(i,index) in package_type_list" :key="index">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="充值单价：">
            <el-input v-model="user_monthly.config_value" readonly></el-input>
          </el-form-item>
          <el-form-item label="充值总额：">
            <el-input v-model="total_price" readonly></el-input>
          </el-form-item>
<!--          <el-form-item label="发票信息：">-->
<!--            <el-input v-model="rechargeForm.bill_info"></el-input>-->
<!--          </el-form-item>-->
<!--          <el-form-item label="车位数量：">-->
<!--            <el-input v-model="rechargeForm.park_lot"></el-input>-->
<!--          </el-form-item>-->
          <el-form-item label="备注：">
            <el-input v-model="rechargeForm.remark"></el-input>
          </el-form-item>
        </div>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="rechargeFormVisible = false">取消</el-button>
        <el-button type="primary" @click="addAppUserRecharge">确定</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="表格列编辑"
      :visible.sync="table_dialog_form_visiable"
      style="width: 100%;"
      custom-class="dialogwidth">
      <el-form
        ref="dataForm"
        label-position="left"
        label-width="200px">
        <div class="lb">
          <el-form-item :label="ctl.label + '：'" v-for="(ctl,index) in table_ctl" :key="index">
            <el-switch v-model="ctl.visiable" active-text="可见" inactive-text="不可见" active-color="#13ce66" inactive-color="#666666"></el-switch>
          </el-form-item>
        </div>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="table_dialog_form_visiable = false" type='primary'>确定</el-button>
      </div>
    </el-dialog>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.page"
      :page-sizes="[20,50,99]"
      :page-size="99"
      :limit.sync="listQuery.limit"
      @pagination="getAppUserList"
    />
  </div>
</template>

<script>
  import Pagination from "@/components/Pagination/index"; // secondary package based on el-pagination
  import XLSX from 'xlsx'
  import Vue from "vue";
  import { getAppUser, addAppUser, editAppUser, removeAppUser, addAppUserPackage, uploadUserList, exportCountReport, getMerchant } from '@/api/user'
  import { getSysConfig } from '@/api/system'

  export default {
    components: {Pagination},
    data() {
      return {
        list:[],
        total:0,
        rechargeForm: {},
        user_monthly:{},
        fileList:[],
        excelLoading:false,
        table_dialog_form_visiable:false,
        excelData: {
          header: null,
          results: null
        },
        table_ctl:[
          {
            label:'ID',
            prop:'id',
            sortable: false,
            visiable:true,
            formatter:null,
          },
          {
            label:'姓名',
            prop:'realname',
            sortable: false,
            visiable:true,
            formatter:null,
          },
          {
            label:'用户名',
            prop:'username',
            sortable: false,
            visiable:true,
            formatter:null,
          },
          // {
          //   label:'手机',
          //   prop:'user_phone',
          //   sortable: false,
          //   visiable:true,
          //   formatter:null,
          // },
          {
            label:'电话号码',
            prop:'telephone',
            sortable: false,
            visiable:true,
            formatter:null,
          },
          {
            label:'特殊账户',
            prop:'is_special',
            sortable: false,
            visiable:true,
            formatter:this.tableFormat,
          },


          {
            label:'更新时间',
            prop:'update_time',
            sortable: false,
            visiable:true,
            formatter:this.tableFormat,
          },
          {
            label:'创建时间',
            prop:'create_time',
            sortable: false,
            visiable:true,
            formatter:this.tableFormat,
          },
        ],
        total_price:0,
        baseUrl: process.env.VUE_APP_BASE_API,
        package_type_list: [
          { value: 1, label: '1个月' }, { value: 3, label: '3个月' }, { value: 6, label: '6个月' }, { value: 12, label: '12个月' }],
        rechargeFormVisible: false,
        role_list:['商家','管理员',],
        listLoading:false,
        form:{},
        dialogFormVisible:false,
        textMap: {
          edit: '编辑用户',
          create: '添加用户'
        },
        dialogStatus: '',
        listQuery: {
          page: 1,
          limit: 99,
          start_time: null,
          end_time: null,
        },
      };
    },
    created() {
      this.getAppUserList();
      // this.getConfig();
    },
    mounted() {
    },
    methods: {
      //导出表格
      exportTable() {
        let _this = this
        let para = {
          export:1
        }
        exportCountReport(para).then(res => {
          console.log(res)
          if (res.data.code === 50001) {
            _this.$message({
              type: 'error',
              message: res.data.message,
              duration: 2500
            })
            return
          }
          if (res.data.type) {
            // 文件下载
            const blob = new Blob([res.data], {
              type: "application/vnd.ms-excel"
            });
            let link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.setAttribute('download', '导出文件.xlsx');
            link.click();
            link = null;
          } else {
            _this.$message({
              type: 'error',
              message: '导出出错',
              duration: 2500
            })
            // 返回json
          }
        })

      },
      //获取app用户数据
      getAppUserList() {
        let _this = this
        _this.listLoading = true
        getAppUser(_this.listQuery).then(response => {
          _this.list = response.items;
          _this.total = response.totalCount;
          _this.listLoading = false
        });
      },
      //获取收费标准
      getConfig() {
        let _this = this
        getSysConfig({ filter: { config_class: 'user_monthly' } }).then(response => {
          response.items.forEach(ele=>{
            switch (ele.config_key) {
              case 'custom_month_pack_price':
                _this.user_monthly = ele
                break
            }
          })
        })
      },
      //添加app用户
      addAppUser(){
        let _this = this
        let para = Object.assign({},_this.form)
        // para.roles = [para.roles]
        addAppUser(para).then((res) => {
          if (res.code === 20000) {
            _this.$message({
              type: "success",
              message: '添加成功',
            });
            _this.dialogFormVisible = false
            _this.getAppUserList()
          }
        });
      },
      //技术套餐总价
      packageTypeSelect(evt){
        switch (evt) {
          case 0:
            this.total_price = parseInt(this.user_monthly.config_value)
            break;
          case 1:
            this.total_price = parseInt(this.user_monthly.config_value) * 3
            break;
          case 2:
            this.total_price = parseInt(this.user_monthly.config_value) * 6
            break;
          case 3:
            this.total_price = parseInt(this.user_monthly.config_value) * 12
            break;
        }
      },
      //显示添加app用户dialogue
      addAppUserForm(){
        this.resetForm()
        this.dialogStatus = 'create'
        this.dialogFormVisible = true
      },
      //显示修改app用户dialogue
      updateAppUserForm(row){
        this.resetForm()
        this.form = row
        this.dialogStatus = 'edit'
        this.dialogFormVisible = true
      },
      // handleUpload(e){
      //   console.log(e)
      // },
      //充值表单数据
      resetForm(){
        this.form = {}
        this.rechargeForm = {valid_months:1,valid:true}
      },
      //显示app用户充值dialogue
      rechargeAppUserForm(row) {
        this.resetForm()
        this.form = Object.assign({},row)
        this.total_price = parseInt(this.user_monthly.config_value)
        this.rechargeFormVisible = true
      },
      //修改app用户
      editAppUser(){
        let _this = this
        let para = Object.assign({},_this.form)
        // para.roles = [para.roles]
        delete para.update_time
        editAppUser(para).then((res) => {
          if (res.code === 20000) {
            _this.$message({
              type: "success",
              message: '修改成功！',
            });
            _this.dialogFormVisible = false
            _this.getAppUserList()
          }
        });
      },
      //app用户充值
      addAppUserRecharge(){
        let _this = this
        let para = {
          user_id:_this.form.id,
          valid_months:_this.form.valid_months
        }
        para.user_id = _this.form.id
        // para.roles = [para.roles]
        addAppUserPackage(para).then((res) => {
          if (res.code === 20000) {
            _this.$message({
              type: "success",
              message: res.message,
            });
            _this.rechargeFormVisible = false
            _this.getAppUserList()
          }
        });

      },
      //删除app用户
      removeAppUser(row){
        let _this = this
        removeAppUser(row.id).then((res) => {
          if (res.code === 20000) {
            _this.$message({
              type: "success",
              message: "删除成功!",
            });
            _this.getAppUserList()
          }
        });

      },
      // 格式化表格数据
      tableFormat(row, column, index) {
        // console.log(row, column, index)
        let str = ''
        switch (column.property) {
          case 'update_time':
            str = row.update_time?row.update_time.slice(0,10):''
            break
          case 'create_time':
            str = row.create_time.slice(0,10)
            break
          case 'is_manager':
            str = row.is_manager ? '是':'否'
            break
          case 'is_block':
            str = row.is_block ? '是':'否'
            break
          case 'is_special':
            str = row.is_special ? '是':'否'
            break
        }
        return str
      },
      // 显示删除弹窗
      showDeleteForm(row) {
        this.$confirm('确定删除用户「' + row.nick_name + '」吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.removeAppUser(row)
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除',
            duration:500,
          })
        })
      },
      handleDrop(e) {
        e.stopPropagation()
        e.preventDefault()
        if (this.excelLoading) return
        const files = e.dataTransfer.files
        if (files.length !== 1) {
          this.$message.error('Only support uploading one file!')
          return
        }
        const rawFile = files[0] // only use files[0]
        if (!this.isExcel(rawFile)) {
          this.$message.error(
            'Only supports upload .xlsx, .xls, .csv suffix files'
          )
          return false
        }
        this.upload(rawFile)
        e.stopPropagation()
        e.preventDefault()
      },
      handleDragover(e) {
        e.stopPropagation()
        e.preventDefault()
        e.dataTransfer.dropEffect = 'copy'
      },
      handleUpload() {
        this.$store.getters.user
        this.$refs['excel-upload-input'].click()
      },
      handleClick(e) {
        const files = e.target.files
        const rawFile = files[0] // only use files[0]
        if (!rawFile) return
        this.upload(rawFile)
      },
      upload(rawFile) {
        this.$refs['excel-upload-input'].value = null // fix can't select the same excel
        if (!this.beforeUpload) {
          this.readerData(rawFile)
          return
        }
        const before = this.beforeUpload(rawFile)
        if (before) {
          this.readerData(rawFile)
        }
      },
      readerData(rawFile) {
        this.excelLoading = true
        return new Promise((resolve, reject) => {
          const reader = new FileReader()
          reader.onload = (e) => {
            const data = e.target.result
            const workbook = XLSX.read(data, { type: 'array' })
            const firstSheetName = workbook.SheetNames[0]
            const worksheet = workbook.Sheets[firstSheetName]
            const header = this.getHeaderRow(worksheet)
            const results = XLSX.utils.sheet_to_json(worksheet)
            this.generateData({ header, results })
            this.excelLoading = false
            resolve()
          }
          reader.readAsArrayBuffer(rawFile)
        })
      },
      generateData({ header, results }) {
        this.excelData.header = header

        // 拆分数据 ，把盘口数据拆分到attr中
        this.excelData.results = results
        this.onSuccess && this.onSuccess(this.excelData)
        // 拆分数据提交到后端
        this.submitExcelData()
      },
      getHeaderRow(sheet) {
        const headers = []
        const range = XLSX.utils.decode_range(sheet['!ref'])
        let C
        const R = range.s.r
        /* start in the first row */
        for (C = range.s.c; C <= range.e.c; ++C) {
          /* walk every column in the range */
          const cell = sheet[XLSX.utils.encode_cell({ c: C, r: R })]
          /* find the cell in the first row */
          let hdr = 'UNKNOWN ' + C // <-- replace with your desired default
          if (cell && cell.t) hdr = XLSX.utils.format_cell(cell)
          headers.push(hdr)
        }
        return headers
      },
      isExcel(file) {
        return /\.(xlsx|xls|csv)$/.test(file.name)
      },
      submitExcelData(){
        let _this = this
        let user_list = []
        this.excelData.results.forEach(ele=>{
          let tmp = {
            real_name:ele['姓名'],
            number:ele['工号'],
          }
          user_list.push(tmp)
        })
        uploadUserList({ user_list }).then((res) => {
          if (res.code === 20000) {
            _this.$message({
              type: "success",
              message: res.message,
            });
            _this.dialogFormVisible = false
            _this.getAppUserList()
          }
        });
      },
    }
  };
</script>

<style>
  body .el-table tr:first-child th{
    font-size: 15px;
    color: black;
    border-bottom: 2px solid #999999;
  }
</style>

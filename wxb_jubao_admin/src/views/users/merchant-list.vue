<template>
  <div class="app-container">
    <div class="filter-container">
      <el-row>
        <el-col :span="4">
          <el-input
            v-model="listQuery.filter.id"
            placeholder="ID"
            style="width: 90%;margin-left: 5%;"
            class="filter-item"
            clearable
          />
        </el-col>
        <el-col :span="4">
          <el-input
            v-model="listQuery.filter.account"
            placeholder="账号"
            style="width: 90%;margin-left: 5%;"
            class="filter-item"
            clearable
          />
        </el-col>
        <el-col :span="4">
          <el-input
            v-model="listQuery.filter.business_name"
            placeholder="商户名称"
            style="width: 90%;margin-left: 5%;"
            class="filter-item"
            clearable
          />
        </el-col>
        <el-col :span="4">
          <el-input
            v-model="listQuery.filter.serial_no"
            placeholder="商家编号"
            style="width: 90%;margin-left: 5%;"
            class="filter-item"
            clearable
          />
        </el-col>
        <el-col :span="4">
          <span>  </span>
        </el-col>
        <!--        <el-col :span="4">-->
        <!--          <el-button-->
        <!--            class="filter-item"-->
        <!--            type="info"-->
        <!--            icon="el-icon-download"-->
        <!--            style="float: right;"-->
        <!--            size="mini"-->
        <!--            @click="exportTable2"-->
        <!--          >导出报表2-->
        <!--          </el-button>-->
        <!--        </el-col>-->
        <el-col :span="4">
          <el-button
            class="filter-item"
            type="info"
            icon="el-icon-download"
            style="float: right;"
            size="mini"
            @click="exportTable"
          >导出报表
          </el-button>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="4">
          <el-input
            v-model="listQuery.filter.user_name"
            placeholder="用户姓名"
            style="width: 90%;margin-left: 5%;"
            class="filter-item"
            clearable
          />
        </el-col>
        <el-col :span="4">
          <el-input
            v-model="listQuery.filter.user_phone"
            placeholder="用户电话"
            style="width: 90%;margin-left: 5%;"
            class="filter-item"
            clearable
          />
        </el-col>
        <el-col :span="4">
          <el-select clearable placeholder="禁用状态" style="width: 90%;margin-left: 5%;" v-model="listQuery.filter.valid">
            <el-option :label="i.label" :value="i.value" v-for="(i,index) in valid_list" :key="index">
            </el-option>
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select clearable placeholder="包月类型" style="width: 90%;margin-left: 5%;"
                     v-model="listQuery.filter.package_type">
            <el-option :label="i.label" :value="i.value" v-for="(i,index) in package_type_list" :key="index">
            </el-option>
          </el-select>
        </el-col>
        <el-col :span="4">
          <span>  </span>
        </el-col>
        <el-col :span="4">
          <el-button
            class="filter-item"
            type="primary"
            size="mini"
            icon="el-icon-plus"
            style="float: right;"
            @click="addMerchantForm"
          >添加商家
          </el-button>
        </el-col>
      </el-row>

      <el-row>

        <el-col :span="4">
          <el-input
            v-model="listQuery.filter.package_remain_times.from"
            placeholder="剩余次数起"
            type="number"
            style="width: 90%;margin-left: 5%;"
            class="filter-item"
          />
        </el-col>
        <el-col :span="4">
          <el-input
            v-model="listQuery.filter.package_remain_times.to"
            type="number"
            placeholder="剩余次数止"
            style="width: 90%;margin-left: 5%;"
            class="filter-item"
          />
        </el-col>
        <el-col :span="4">
          <el-date-picker
            v-model="listQuery.start_time"
            type="date"
            style="width: 90%;margin-left: 5%;"
            value-format="yyyy-MM-dd"
            placeholder="到期起">
          </el-date-picker>
        </el-col>
        <el-col :span="4">
          <el-date-picker
            v-model="listQuery.end_time"
            type="date"
            style="width: 90%;margin-left: 5%;"
            value-format="yyyy-MM-dd"
            placeholder="到期止">
          </el-date-picker>
        </el-col>
        <el-col :span="4">
          <el-button
            class="filter-item"
            size="mini"
            type="warning"
            style="width: 60px;"
            @click="getMerchantList"
          >搜索
          </el-button>
          <el-button
            class="filter-item"
            type="info"
            size="mini"
            style="width: 60px;"
            @click="resetFilter"
          >重置
          </el-button>
        </el-col>
      </el-row>
    </div>
            <el-table
              empty-text="-"
              v-loading="listLoading"
      :data="list"
      fit
      highlight-current-row
      style="width: 100%;">
      <el-table-column type="index" width="50"/>
      <el-table-column v-for="(ctl,index) in table_ctl" :formatter="ctl.formatter" :sortable="ctl.sortable" :key="index"
                       :label="ctl.label" :prop="ctl.prop" v-if="ctl.visiable"
                       align="center"></el-table-column>
      <el-table-column label="状态" prop="valid" align="center">
        <template slot-scope="scope">
          <span :style="scope.row.valid?'color:limegreen;':'color:grey;'">{{scope.row.valid?' 启用':' 禁用'}}</span>
        </template>
      </el-table-column>
      <el-table-column label="到期日期/剩余次数" prop="lot_count" align="center">
        <template slot-scope="scope">
          {{scope.row.package_type===0?scope.row.package_remain_times + '次':scope.row.month_package_valid_to}}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200px">
        <template slot="header" slot-scope="scope">
          <span style="margin-right: 15px">操作</span>
          <el-tooltip class="item" effect="dark" :hide-after="500" content="设置列" placement="top">
            <el-button icon="el-icon-setting" @click="table_dialog_form_visiable = true" circle type="text"></el-button>
          </el-tooltip>
        </template>
        <template slot-scope="scope">
          <el-tooltip class="item" effect="dark" :hide-after="500" content="编辑" placement="top">
            <el-button icon="el-icon-edit" class="my-blue" circle size="medium" @click="updateMerchantForm(scope.row)"
                       ></el-button>
          </el-tooltip>
          <el-tooltip class="item" effect="dark" :hide-after="500" content="充值" placement="top">
            <el-button class="my-green" circle size="medium" @click="rechargeMerchantForm(scope.row)"
                       >￥</el-button>
          </el-tooltip>
          <el-tooltip class="item" effect="dark" :hide-after="500" content="查看消费记录" placement="top">
            <el-button icon="el-icon-search" circle size="medium" @click="consumingRecordsShow(scope.row)"
                       class="grey"></el-button>
          </el-tooltip>
          <el-tooltip class="item" effect="dark" :hide-after="500" content="删除" placement="top">
            <el-button icon="el-icon-delete" class="my-red" circle size="medium"
                       @click="showDeleteForm(scope.row)"></el-button>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      style="width: 100%;"
      custom-class="dialogwidth">
      <el-form
        ref="dataForm"
        :model="form"
        label-position="left"
        label-width="200px">
        <div class="lb">
          <el-form-item label="商户编号：">
            <el-input v-model="form.serial_no"></el-input>
          </el-form-item>
          <el-form-item label="商户名称：">
            <el-input v-model="form.business_name"></el-input>
          </el-form-item>
          <el-form-item label="用户姓名：">
            <el-input v-model="form.user_name"></el-input>
          </el-form-item>
          <el-form-item label="账号：">
            <el-input v-model="form.account"></el-input>
          </el-form-item>
          <el-form-item label="密码：">
            <el-input v-model="form.password" show-password></el-input>
          </el-form-item>
          <el-form-item label="用户电话：">
            <el-input v-model="form.user_phone"></el-input>
          </el-form-item>
          <el-form-item label="车位数量：">
            <el-input v-model="form.lot_count"></el-input>
          </el-form-item>
          <el-form-item label="充值类型：" v-if="!form.id">
            <el-select style="width: 100%;" v-model="form.package_type">
              <el-option :label="i.label" :value="i.value" v-for="(i,index) in package_type_list" :key="index">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="充值类型：" v-if="form.id">
            <el-select style="width: 100%;" v-model="form.package_type">
              <el-option :disabled="formTableDisableChecker()" :label="i.label" :value="i.value"
                         v-for="(i,index) in package_type_list" :key="index">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="状态：">
            <el-switch v-model="form.valid"></el-switch>
          </el-form-item>
        </div>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="dialogStatus==='create'?addMerchant():editMerchant()">确定</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="商户充值"
      :visible.sync="rechargeFormVisible"
      style="width: 100%;"
      custom-class="dialogwidth">
      <el-form
        ref="dataForm"
        :model="rechargeForm"
        label-position="left"
        label-width="150px">
        <div class="lb">
          <el-form-item label="商户ID：">
            <el-input v-model="form.id" readonly></el-input>
          </el-form-item>
          <el-form-item label="商户名称：">
            <el-input v-model="form.business_name" readonly></el-input>
          </el-form-item>
          <el-form-item label="充值类型：">
            <el-select style="width: 100%;" v-model="form.package_type">
              <el-option disabled :label="i.label" :value="i.value" v-for="(i,index) in package_type_list" :key="index">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="充值次数：" v-if="form.package_type===recharge_type.time">
            <el-input v-model="rechargeForm.valid_times"
                      @input="rechargeForm.valid_times=rechargeForm.valid_times.replace(/\D/g,''),chargeNumChange(rechargeForm.valid_times)"></el-input>
          </el-form-item>
          <el-form-item label="充值月数：" v-if="form.package_type===recharge_type.monthly">
            <el-input v-model="rechargeForm.valid_months"
                      @input="rechargeForm.valid_months=rechargeForm.valid_months.replace(/\D/g,''),chargeNumChange(rechargeForm.valid_months)"></el-input>
          </el-form-item>
          <el-form-item label="充值单价：" v-if="form.package_type===recharge_type.time">
            <el-col :span="18">
              <el-input v-model="business_timely_tmp.config_value"
                                         @input="business_timely_tmp.config_value = business_timely_tmp.config_value.replace(/[^\d]/g,''),chargeNumChange(rechargeForm.valid_times)"></el-input>
            </el-col>
            <el-col :span="6"><el-button type="primary" @click="resetPrice('business_timely',rechargeForm.valid_times)" style="float: right">重置</el-button></el-col>
          </el-form-item>
          <el-form-item label="充值单价：" v-if="form.package_type===recharge_type.monthly">
            <el-col :span="18">
              <el-input v-model="business_monthly_tmp.config_value"
                      @input="business_monthly_tmp.config_value = business_monthly_tmp.config_value.replace(/[^\d]/g,''),chargeNumChange(rechargeForm.valid_months)"></el-input>
            </el-col>
            <el-col :span="6"><el-button type="primary" @click="resetPrice('business_monthly',rechargeForm.valid_months)" style="float: right">重置</el-button></el-col>
          </el-form-item>
          <el-form-item label="充值总额：">
            <el-input v-model="total_price" @input="total_price = total_price.replace(/\D/g,'')" ></el-input>
          </el-form-item>
          <el-form-item label="备注：">
            <el-input v-model="rechargeForm.remark"></el-input>
          </el-form-item>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="rechargeFormVisible = false">取消</el-button>
        <el-button type="primary" @click="addMerchantRecharge()">确定</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="消费记录"
      :visible.sync="consumingRecordsVisible"
      style="width: 100%;"
      custom-class="dialogwidth">
      <el-table :data="consumingRecords">
        <el-table-column
          v-for="(ctl,index) in table_ctl2"
          v-if="ctl.visiable"
          :key="index"
          :formatter="ctl.formatter"
          :sortable="ctl.sortable"
          :label="ctl.label"
          :prop="ctl.prop"
          align="center"
        />
      </el-table>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="consumingRecordsVisible = false">确定</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="表格列编辑"
      :visible.sync="table_dialog_form_visiable"
      style="width: 100%;"
      custom-class="dialogwidth">
      <el-form
        ref="dataForm"
        label-position="left"
        label-width="200px">
        <div class="lb">
          <el-form-item :label="ctl.label + '：'" v-for="(ctl,index) in table_ctl" :key="index">
            <el-switch v-model="ctl.visiable" active-text="可见" inactive-text="不可见" active-color="#13ce66"
                       inactive-color="#666666"></el-switch>
          </el-form-item>
        </div>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="table_dialog_form_visiable = false" type='primary'>确定</el-button>
      </div>
    </el-dialog>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.page"
      :page-sizes="[20,50,99]"
      :page-size="99"
      :limit.sync="listQuery.limit"
      @pagination="getMerchantList"
    />
  </div>
</template>

<script>
  import Pagination from '@/components/Pagination/index' // secondary package based on el-pagination
  import {
    getMerchant,
    addMerchant,
    editMerchant,
    removeMerchant,
    getMerchantDetail,
    addBusinessPackage
  } from '@/api/user'
  import Vue from 'vue'
  import qs from 'qs';
  import {getSysConfig} from '@/api/system'
  import { getOrder, getParkRecord } from '@/api/manage'
  import { mapGetters } from 'vuex'

  export default {
    components: {Pagination},
    data() {
      return {

        list: [],
        total: 0,
        listLoading: false,
        form: {},
        total_price:0,
        rechargeForm: {},
        recharge_type: {time: 0, monthly: 1},
        rechargeFormVisible: false,
        dialogTimeFormVisible: false,
        dialogFormVisible: false,
        payTypeList: [
          '按次', '包月'
        ],
        table_dialog_form_visiable: false,
        table_ctl: [
          {
            label: 'ID',
            prop: 'id',
            sortable: false,
            visiable: true,
            formatter: null
          },
          {
            label: '账号',
            prop: 'account',
            sortable: false,
            visiable: true,
            formatter: null
          },
          {
            label: '商户名称',
            prop: 'business_name',
            sortable: false,
            visiable: true,
            formatter: null
          },
          {
            label: '商户编号',
            prop: 'serial_no',
            sortable: false,
            visiable: true,
            formatter: null
          },
          {
            label: '用户姓名',
            prop: 'user_name',
            sortable: false,
            visiable: true,
            formatter: null
          },
          {
            label: '用户电话',
            prop: 'user_phone',
            sortable: false,
            visiable: true,
            formatter: null
          },
          {
            label: '车位数量',
            prop: 'lot_count',
            sortable: false,
            visiable: true,
            formatter: null
          },
          {
            label: '充值类型',
            prop: 'package_type',
            sortable: false,
            visiable: true,
            formatter: this.tableFormat
          },
          {
            label: '创建时间',
            prop: 'create_time',
            sortable: false,
            visiable: true,
            formatter: this.tableFormat
          },
        ],
        consumingRecordsVisible: false,
        business_monthly: {},
        business_timely: {},
        business_timely_tmp:{},
        business_monthly_tmp:{},
        package_type_list: [
          {value: 0, label: '按次'}, {value: 1, label: '包月'}],
        valid_list: [
          {value: true, label: '启用'}, {value: false, label: '禁用'}],
        zero: '0',
        consumingRecords: [],

        table_ctl2: [
          {
            label: 'ID',
            prop: 'id',
            sortable: false,
            visiable: true,
            formatter: null
          },
          {
            label: '车牌',
            prop: 'plate_number',
            sortable: false,
            visiable: true,
            formatter: null
          },
          {
            label: '支付类型',
            prop: 'business_pack_type',
            sortable: false,
            visiable: true,
            formatter: this.tableFormat2
          },
          {
            label: '总费用',
            prop: 'total_fee',
            sortable: false,
            visiable: true,
            formatter: null
          },
          {
            label: '核销时间',
            prop: 'validity_time',
            sortable: false,
            visiable: true,
            formatter: null
          },
        ],
        textMap: {
          edit: '编辑商家',
          create: '添加商家'
        },
        timeSetList: [30, 120, 180],
        dialogStatus: '',
        listQuery: {
          page: 1,
          limit: 99,
          filter: {package_remain_times: {}}
        }
      }
    },
    created() {
      this.getMerchantList()
      this.getConfig()
    },
    mounted() {
    },
    computed: {
      ...mapGetters([
        'user_info'
      ])
    },
    methods: {
      //获取商家数据
      getMerchantList() {
        let _this = this
        _this.listLoading = true
        let para = Object.assign({}, _this.listQuery)
        para = _this.clearObj(para)
        getMerchant(para).then(response => {
          _this.list = response.items
          _this.total = response.totalCount
          _this.listLoading = false
        })
      },
      //获取收费标准
      getConfig() {
        let _this = this
        let price_str = []
        getSysConfig({filter: {config_class: 'business',lot_id:_this.user_info.lot_id}}).then(response => {
          response.items.forEach(ele => {
            switch (ele.config_key) {
              case 'business_month_pack_price':
                _this.business_monthly = ele
                price_str.push('包月单价：' + ele.config_value)
                break
              case 'business_times_pack_price':
                _this.business_timely = ele
                price_str.push('单次单价：' + ele.config_value)
                break
              case 'business_ticket_max':
                // Vue.set(this.timeList[0], 'charge2', ele.config_value)
                break
            }
          })
          _this.changeTitle(price_str)
        })
      },
      //导出表格
      exportTable() {
        let _this = this
        let para = _this.clearObj(Object.assign({}, this.listQuery))
        // let para = {}
        delete para.limit
        delete para.page
        para.export = 1
        // exportFile('business')
        getMerchant(para).then(res => {
          console.log(res)
          if (res.data.code === 50001) {
            _this.$message({
              type: 'error',
              message: res.data.message,
              duration: 2500
            })
            return
          }
          if (res.data.type) {
            // 文件下载
            const blob = new Blob([res.data], {
              type: "application/vnd.ms-excel"
            });
            let link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.setAttribute('download', '导出文件.xlsx');
            link.click();
            link = null;
          } else {
            _this.$message({
              type: 'error',
              message: '导出出错',
              duration: 2500
            })
            // 返回json
          }
        })

      },

      //获取商家收费记录
      getParkingRecordList(row) {
        let _this = this
        getOrder({filter: {payer_id: row.id,purpose: [1,3],}}).then(response => {
          _this.consumingRecords = response.items
        })
      },
      //显示添加商家dialogue
      addMerchantForm() {
        this.resetForm()
        this.dialogStatus = 'create'
        this.form.BALANCE = 0
        this.dialogFormVisible = true
      },
      //添加商家
      addMerchant() {
        let _this = this
        let para = Object.assign({}, _this.form)
        // para.roles = [para.roles]
        addMerchant(para).then((res) => {
          if (res.code === 20000) {
            _this.$message({
              type: 'success',
              message: res.message
            })
            _this.dialogFormVisible = false
            _this.getMerchantList()
          }
        })
      },
      //修改添加商家dialogue
      updateMerchantForm(row) {
        this.resetForm()
        this.form = row
        this.dialogStatus = 'edit'
        this.dialogFormVisible = true
      },
      //设置总价
      chargeNumChange(evt) {
        this.total_price = parseInt(evt) * parseInt(this.form.package_type === this.recharge_type.monthly ? this.business_monthly_tmp.config_value : this.business_timely_tmp.config_value)
      },
      //重置单价
      resetPrice(evt,evt2) {
        this[evt + '_tmp'] = Object.assign({}, this[evt])
        this.total_price = parseInt(evt2) * parseInt(this.form.package_type === this.recharge_type.monthly ? this.business_monthly_tmp.config_value : this.business_timely_tmp.config_value)
      },
      //修改商家
      editMerchant() {
        let _this = this
        let para = Object.assign({}, _this.form)
        // para.roles = [para.roles]
        delete para.update_time
        editMerchant(para).then((res) => {
          if (res.code === 20000) {
            _this.$message({
              type: 'success',
              message: res.message
            })
            _this.dialogFormVisible = false
            _this.getMerchantList()
          }
        })
      },
      //显示添加商家充值dialogue
      rechargeMerchantForm(row) {
        this.resetForm()
        this.form = Object.assign({}, row)
        this.business_timely_tmp = Object.assign({}, this.business_timely)
        this.business_monthly_tmp = Object.assign({}, this.business_monthly)
        this.rechargeFormVisible = true
      },
      //商家充值
      addMerchantRecharge() {
        let _this = this
        if(!_this.total_price){
          return
        }
        let para = Object.assign(_this.rechargeForm)
        para.valid_times ? para.valid_times = parseInt(para.valid_times) : ''
        para.valid_months ? para.valid_months = parseInt(para.valid_months) : ''
        para.user_id = _this.form.id
        para.package_type = _this.form.package_type
        para.charge_price = parseInt(para.package_type == _this.recharge_type.monthly ? _this.business_monthly_tmp.config_value : _this.business_timely_tmp.config_value)
        para.charge_total = _this.total_price
        addBusinessPackage(para).then((res) => {
          if (res.code === 20000) {
            _this.$message({
              type: 'success',
              message: res.message
            })
            _this.rechargeFormVisible = false
            _this.getMerchantList()
          }
        })

      },
     //重置表单
      resetForm() {
        this.form = {package_type: 0, valid: true}
        this.rechargeForm = {package_type: 0}
      },
      //删除商家
      removeMerchant(row) {
        let _this = this
        removeMerchant(row.id).then((res) => {
          if (res.code === 20000) {
            _this.$message({
              type: 'success',
              message: '删除成功!'
            })
            _this.getMerchantList()
          }
        })
      },
      //显示停车记录dialogue
      consumingRecordsShow(row) {
        this.consumingRecordsVisible = true
        this.getParkingRecordList(row)
      },
      //存在停车套餐时候禁止修改套餐类型
      formTableDisableChecker() {
        let curTime = new Date()
        let vali_to = this.form.month_package_valid_to
        let endtime = vali_to !== null ? new Date(Date.parse(vali_to)) : new Date()
        return !(this.form.package_remain_times === 0 && endtime <= curTime)
      },
      // 格式化表格数据
      tableFormat(row, column, index) {
        // console.log(row, column, index)
        let str = ''
        switch (column.property) {
          case 'package_type':
            this.payTypeList.forEach((ele, index) => {
              if (index === row.package_type) {
                str = ele
              }
            })
            break
          case 'purpose':
            this.purposeList.forEach((ele, index) => {
              if (index === row.purpose) {
                str = ele
              }
            })
            break
          case 'create_time':
            str = row.create_time.slice(0, 10)
            break
        }
        return str
      },
      // 格式化表格数据
      tableFormat2(row, column, index) {
        // console.log(row, column, index)
        let str = ''
        switch (column.property) {
          case 'update_time':
            str = row.update_time ? row.update_time.slice(0, 10) : ''
            break
          case 'create_time':
            str = row.create_time.slice(0, 10)
            break
          case 'out_handler':
            str = row.out_handler == '' ? '系统放行' : row.out_handler
            break
          case 'business_pack_type':
            str = row.business_pack_type == 1 ? '包月' : '按次'
            break

          // case 'out_time':
          //   str = row.out_time ? row.out_time.slice(0, 10) : ''
          //   break
        }
        return str
      },
      // 显示删除弹窗
      showDeleteForm(row) {
        this.$confirm('确定删除商家「' + row.user_name + '」吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.removeMerchant(row)
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除',
            duration: 500
          })
        })
      },
      //修改标题显示的单价
      changeTitle(arr) {
        this.$store
          .dispatch('user/resetPrice', arr)
          .then(() => {
          })
          .catch(() => {
          })
      },
      // 重置表单数据
      resetFilter() {
        this.listQuery = {
          page: 1,
          limit: 99,
          filter: {package_remain_times: {}}
        }
        this.getMerchantList()
      },
      //去掉无用的数据
      clearObj(obj) {
        for (var ob in obj) {
          if (obj[ob] == null || obj[ob] === '') {
            delete obj[ob]
          } else if (typeof obj[ob] === 'object') {
            obj[ob] = this.clearObj(obj[ob])
          }
        }
        return obj
      },
    }
  }
</script>
<style>
  body .el-table tr:first-child th{
    font-size: 15px;
    color: black;
    border-bottom: 2px solid #999999;
  }
  .grey{
    color: white;background: gray;
  }
</style>

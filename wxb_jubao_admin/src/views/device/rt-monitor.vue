<template>
  <div class="app-container">
    <div class="flex-row" style="width: 100%;height: 100%;">
      <div style="" class="flex-column">
        <div style="width: 100%;height: calc(50% - 15px);">
          <div class="content-bar solid-border">
            <div class="content-title grey">最近入场纪录
              <!--              <el-input v-model="form.ip" style="width: 25%;" placeholder="摄像头IP" @input="videoFormInput" type=""></el-input>-->
              <!--              <el-input v-model="form.username" style="width: 20%;" type="" @input="videoFormInput" placeholder="用户名"></el-input>-->
              <!--              <el-input v-model="form.password" style="width: 20%;" type="" @input="videoFormInput" placeholder="密码"></el-input>-->
              <el-button style="margin: 7px 20px" v-if="gate" @click="startPlay(1)" type="warning">刷新视频</el-button>
              <el-input v-if="testing" style="width: 150px" v-model="inPlate.sn" placeholder="序列号"></el-input>
              <el-input v-if="testing" style="width: 150px" v-model="inPlate.plate" placeholder="车牌"></el-input>
              <el-tooltip class="item" effect="dark" :hide-after="5000" content="入场时间与当前时间差（秒）" placement="top">
                <el-input v-if="testing" style="width: 150px" v-model="inPlate.timestmp" placeholder="时间差"></el-input>
              </el-tooltip>
              <el-tooltip class="item" effect="dark" :hide-after="5000" content="使用当前时间戳" placement="top">
                <el-switch v-if="testing" v-model="inPlate.now">现在</el-switch>
              </el-tooltip>
              <el-button v-if="testing" @click="simulateIn(inPlate)">模拟入场</el-button>
            </div>
            <div style="display: flex;flex-direction: row;width: 100%;height: 100%;">
              <div style="width: 50%" v-if="gate">
                <div id='video1' class="rt-video">
                  <div v-if="!video_success">
                    <el-icon class="el-icon-loading"></el-icon>
                    加载中,加载时间过长请手动刷新视频
                  </div>
                </div>
              </div>
              <div style="height: 100%" :style="{width: gate ? '50%':'100%'}">
                <el-table
                  :data="enter_list"
                  empty-text="-"
                  class=""
                  fit
                  highlight-current-row
                  style="width: 100%;height: calc(100% - 50px)">
                  <el-table-column label="车牌号" :width="gate?'100px':''" prop="plate_number" align="center">
                    <template slot-scope="scope">
                      <el-tooltip class="item" effect="dark" :hide-after="5000" content="点击查询车牌" placement="top">
                      <span @click="setPlate(scope.row.plate_number)">
                        {{scope.row.plate_number}}
                      </span>
                      </el-tooltip>
                      <!--                      <el-tooltip class="item" effect="dark" :hide-after="500" content="查询" placement="top">-->
                      <!--                        <el-button icon="el-icon-search" class="my-red" circle size="mini" @click="setPlate(scope.row.plate_number)"/>-->
                      <!--                      </el-tooltip>-->
                    </template>
                  </el-table-column>
                  <el-table-column label="车辆类型" :width="gate?'100px':''" prop="car_type" align="center"/>
                  <el-table-column label="入场时间" prop="in_time" align="center"/>
                  <el-table-column label="入场口" :width="gate?'100px':''" prop="in_gate.name" align="center"/>
                </el-table>
              </div>
            </div>
          </div>
        </div>
        <div style="width: 100%;height: calc(50% - 15px);">
          <div class="content-bar solid-border">
            <div class="content-title grey">最近出场纪录
              <el-button style="margin: 7px 20px" v-if="gate" @click="startPlay(2)" type="warning">刷新视频</el-button>
              <el-input v-if="testing" style="width: 150px" v-model="outPlate.sn" placeholder="序列号"></el-input>
              <el-input v-if="testing" style="width: 150px" v-model="outPlate.plate" placeholder="车牌"></el-input>
              <el-tooltip class="item" effect="dark" :hide-after="5000" content="入场时间与当前时间差（秒）" placement="top">
                <el-input v-if="testing" style="width: 150px" v-model="outPlate.timestmp" placeholder="时间戳"></el-input>
              </el-tooltip>
              <el-tooltip class="item" effect="dark" :hide-after="5000" content="使用当前时间戳" placement="top">
                <el-switch v-if="testing" v-model="outPlate.now">现在</el-switch>
              </el-tooltip>
              <el-button v-if="testing" @click="simulateIn(outPlate)">模拟出场</el-button>
            </div>
            <div style="display: flex;flex-direction: row;width: 100%;height: 100%;">
              <div style="width: 50%" v-if="gate">
                <div id='video2' class="rt-video">
                  <div v-if="!video_success">
                    <el-icon class="el-icon-loading"></el-icon>
                    加载中,加载时间过长请手动刷新视频
                  </div>
                </div>
              </div>
              <div style="width: 50%;height: 100%" :style="{width: gate ? '50%':'100%'}">
                <el-table
                  :data="exit_list"
                  empty-text="-"
                  class="border-bottom content-table"
                  v-loading=""
                  fit
                  highlight-current-row
                  style="width: 100%;height: calc(100% - 50px)">
                  <el-table-column label="车牌号" :width="gate?'100px':''" prop="plate_number" align="center">
                    <template slot-scope="scope">
                      <el-tooltip class="item" effect="dark" :hide-after="5000" content="点击查询车牌" placement="top">
                      <span @click="setPlate(scope.row.plate_number)">
                        {{scope.row.plate_number}}
                      </span>
                      </el-tooltip>
                      <!--                      <el-tooltip class="item" effect="dark" :hide-after="500" content="查询" placement="top">-->
                      <!--                        <el-button icon="el-icon-search" class="my-red" circle size="mini" @click="setPlate(scope.row.plate_number)"/>-->
                      <!--                      </el-tooltip>-->
                    </template>
                  </el-table-column>
                  <el-table-column label="车辆类型" :width="gate?'100px':''" prop="car_type" align="center"/>
                  <el-table-column label="出场时间" prop="out_time" align="center"/>
                  <el-table-column label="停车时长" :width="gate?'100px':''" prop="park_time" align="center"/>
                </el-table>
              </div>
            </div>
          </div>
        </div>
      </div>


      <div style="width: 30%;height: 88vh;">
        <div class="content-bar solid-border">
          <div class="content-title grey">
            当前出口：<span>{{gate?gate.name:''}}</span>
            <el-button style="margin: 7px 20px" v-if="testing" @click="getNowOutPlate()" type="warning">刷新出口信息</el-button>
            <el-button style="margin: 7px 20px" @click="checkPlateVisiable = true" icon="el-icon-search" type="warning">查询车辆信息</el-button>
          </div>
          <!--          <div class="content">-->
          <!--            <video id="my-video" style="width:100%;height:100%;position:relative;" autoplay muted></video>-->
          <!--          </div>-->
          <div class="rt-monitor">
            <div class="should-charge">
              应该收金额：￥{{out_record? out_record.fee : ''}}元
            </div>
            <div class="detail-info">
              <div class="child">
                <span class="label">车牌:</span>
                <el-tooltip class="item" effect="dark" :hide-after="5000" content="点击查询车牌" placement="top">
                  <span class="content" @click="setPlate(out_record? out_record.plate_number : '')"> {{out_record? out_record.plate_number : ''}}</span>
                </el-tooltip>
              </div>
              <div class="child">
                <span class="label">车辆类型:</span>
                <span class="content">{{out_record? out_record.pack_status : ''}}</span>
              </div>
              <div class="child">
                <span class="label">入场时间:</span>
                <span class="content">{{out_record? out_record.in_time : ''}}</span>
              </div>
              <div class="child">
                <span class="label">出场时间:</span>
                <span class="content">{{out_record? out_record.out_time : ''}}</span>
              </div>
              <div class="child">
                <span class="label">停车时长:</span>
                <span class="content">{{out_record? out_record.park_time : ''}}</span>
              </div>
              <div class="child">
                <span class="label">当前出口:</span>
                <span class="content">{{out_record && out_record.out_gate ? out_record.out_gate.name : ''}}</span>
              </div>

            </div>
            <el-button class="button my-red" @click="breakVisiable = true , break_open_form = {}">开闸</el-button>
          </div>
        </div>
      </div>
    </div>
    <el-dialog
      title="选择岗亭"
      :visible.sync="dutyDialogVisiable"
      style="width: 100%;"
      :close-on-click-modal=false
      :before-close="beClose"
      custom-class="dialogwidth">
      <el-form
        ref="dataForm"
        label-position="right"
        label-width="200px">
        <el-form-item label="岗亭">
          <el-select style="width: 100%;" @change="" v-model="gate" value-key="id">
            <el-option :label="i.name" :value="i" v-for="(i,index) in gate_list" :key="index">
            </el-option>
          </el-select>

        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="setGate">确定</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="手动开闸"
      :visible.sync="breakVisiable"
      style="width: 40%;margin-left: 30%"
      :close-on-click-modal=false
      custom-class="dialogwidth">
      <el-form
        ref="dataForm"
        label-position="right"
        label-width="200px">
        <el-form-item label="开闸原因：">
          <el-select style="width: 100%;" v-model="break_open_form.reason">
            <el-option :label="i" :value="index" v-for="(i,index) in break_reason" :key="index">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="操作人：">
          <span style="width: 100%">{{user_info.name}}</span>
        </el-form-item>
        <el-form-item label="操作出口：">
          <span style="width: 100%">{{gate?gate.name:''}}</span>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="manualOpen">确定</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="查询车牌"
      :visible.sync="checkPlateVisiable"
      style=""
      :close-on-click-modal=true
      custom-class="dialogwidth">
      <el-row>
        <el-col :span="4">
          <el-input
            v-model="listQuery.filter.plate_number"
            placeholder="车牌号"
            style="width: 90%;margin-left: 5%;"
            class="filter-item"
            clearable
          />
        </el-col>
        <el-col :span="4">
          <el-button
            class="filter-item"
            type="warning"
            size="mini"
            style="width: 60px;"
            @click="getParkingRecord()"
          >搜索
          </el-button>
          <el-button
            class="filter-item"
            type="info"
            size="mini"
            style="width: 60px;"
            @click="resetFilter"
          >重置
          </el-button>
        </el-col>
      </el-row>
      <el-table
        v-loading="listLoading2"
        empty-text="-"
        :data="plateInfoList"
        fit
        highlight-current-row
        style="width: 100%;">
        <el-table-column type="index" width="50"/>
        <el-table-column
          v-for="(ctl,index) in table_ctl"
          v-if="ctl.visiable"
          :key="index"
          :formatter="ctl.formatter"
          :sortable="ctl.sortable"
          :label="ctl.label"
          :prop="ctl.prop"
          align="center"
        />
      </el-table>

      <div slot="footer" class="dialog-footer">
        <el-button @click="checkPlateVisiable = false" type="info">关闭</el-button>
      </div>

    </el-dialog>
  </div>
</template>

<script>
  import Pagination from '@/components/Pagination/index' // secondary package based on el-pagination
  import { setSystemUserDuty } from '@/api/system'
  import Vue from 'vue'
  import { getGate, getGateDetail, manualOpenDevice, getNowOutPlate, simulatePlate } from '@/api/device'
  import { mapGetters } from 'vuex'
  import { getParkRecord } from '@/api/manage'
  import axios from 'axios'
  import { decode } from '@/directive/rt-video/rt-video'
  // import JSEncrypt from '@/directive/rt-video/jsencrypt.min'

  export default {
    components: { Pagination },
    data() {
      return {
        list: [],
        out_record: {},
        out_monitor: {},
        enter_list: [],
        exit_list: [],
        intervalID: null,
        total: 0,
        plateInfoList:[],
        table_ctl: [
          {
            label: 'ID',
            prop: 'id',
            sortable: false,
            visiable: true
          },
          {
            label: '车牌号',
            prop: 'plate_number',
            sortable: false,
            visiable: true
          },
          {
            label: '车牌类型',
            prop: 'car_type',
            sortable: false,
            visiable: true
          },
          {
            label: '入场时间',
            prop: 'in_time',
            sortable: false,
            visiable: true
            // formatter: this.tableFormat
          },
          {
            label: '出场时间',
            prop: 'out_time',
            sortable: false,
            visiable: true
            // formatter: this.tableFormat
          },
          {
            label: '停车时长',
            prop: 'park_time',
            sortable: false,
            visiable: true
          },
          {
            label: '应收金额',
            prop: 'should_charge',
            sortable: false,
            visiable: true
          },
          {
            label: '收费金额',
            prop: 'already_paid',
            sortable: false,
            visiable: true
          },
          {
            label: '停车状态',
            prop: 'status',
            sortable: false,
            visiable: true,
            formatter: this.tableFormat
          },
          {
            label: '支付方式',
            prop: 'pay_type',
            sortable: false,
            visiable: true,
            formatter: this.tableFormat
          },
          {
            label: '入场口',
            prop: 'in_gate',
            sortable: false,
            visiable: true,
            formatter: this.tableFormat
          },
          {
            label: '出场口',
            prop: 'out_gate',
            sortable: false,
            visiable: true,
            formatter: this.tableFormat
          },
          {
            label: '出场操作人员',
            prop: 'out_handler',
            sortable: false,
            visiable: true,
            formatter: this.tableFormat
          }
        ],
        breakVisiable: false,
        checkPlateVisiable: false,
        pay_type_list: ['线上⽀付', '线下扫码⽀付', '线下现⾦⽀付', '商户优惠', '月租', '公务车'],
        car_status_list: ['入场', '部分支付', '全额支付', '离场', '已离场'],
        inPlate: { sn: 'fbc44d77-34ed3bbf', plate: localStorage.plate ? localStorage.plate : '冀B40518', timestmp: '3600', now: false },
        outPlate: { sn: '41676fe4-051af89f', plate: localStorage.plate ? localStorage.plate : '冀B40518', timestmp: '3600', now: true },
        player: undefined,
        testing: false,
        role_list: [],
        break_reason: ['无车牌非机动车', '物业公务车', '无入场记录', '线下扫码支付', '线下现金支付'],
        break_open_form: {},
        listLoading: false,
        listLoading2: false,
        gate_list: [{ in_monitor: {}, out_monitor: {} }],
        gate_id: null,
        onduty: false,
        video_success: false,
        form: { ip: '', password: '', username: '' },
        gate: null,
        gate2: null,
        dutyDialogVisiable: false,
        dialogFormVisible: false,
        plate: '',
        textMap: {
          edit: '编辑',
          create: '添加'
        },
        dialogStatus: '',
        listQuery: {
          page: 1,
          limit: 5,
          filter: {}
        },
        listQuery2: {
          page: 1,
          limit: 10,
        }
      }
    },
    created() {
      // this.geRTMonitortList();
      this.checkOnDuty()
      this.getEnterRecordList()
      this.getExitRecordList()
      this.getNowOutPlate()
      let time = this.testing ? 2000 : 2
      this.intervalID = setInterval(() => {
        let _this = this
        if (_this.$route.path !== '/rt-monitor/rt-monitor') {
          window.clearInterval(this.intervalID)
          _this.intervalID = null
        } else {
          _this.getNowOutPlate()
          _this.getEnterRecordList()
          _this.getExitRecordList()
        }
      }, time * 1000)
    },
    mounted() {
      // this.init_video()
      // this.startPlay(1)
    },
    computed: {
      ...mapGetters([
        'roles', 'user_info'
      ])
    },
    watch: {
      plate: {
        handler: function(newVal, oldVal) {
          console.log(`new:${newVal}, old:${oldVal}`)
          if (newVal === '' || !newVal) {

          } else {
            // this.getParkingRecord()
          }
        }
      }

    },
    methods: {
      gateSelect(e) {
        Vue.set(this, 'gate', e)
        console.log(e)
      },
      playAll() {
        this.startPlay(1)
        this.startPlay(2)
      },
      // 获取记录停车数据
      getParkingRecord() {
        const _this = this
        _this.listLoading2 = true
        let para = Object.assign({}, _this.listQuery)
        getParkRecord(para).then(response => {
          if(response.code === 20000){
            _this.plateInfoList = response.items
            _this.listLoading2 = false
          }
        })
      },
      // 重置表单数据
      resetFilter() {
        this.listQuery = {
          page: 1,
          limit: 5,
          filter: {}
        }
        this.getParkingRecord()
      },
      setPlate(number) {
        this.listQuery.filter.plate_number = number
        this.plate = number
        this.getParkingRecord()
        this.checkPlateVisiable = true
      },
      startPlay(video_index) {
        let _this = this
        let monitor = video_index === 1 ? 'in_monitor' : 'out_monitor'
        if (!_this.gate[monitor]) {
          return
        }
        let info = {
          ip: _this.gate[monitor].ip_address,
          username: _this.gate[monitor].admin_account,
          password: _this.gate[monitor].admin_password
        }
        _this.video_success = false
        var ip = info.ip   // 设备局域网IP
        var port = 80              // 设备网页端口
        var proto = 'http'         // 协议，http 或 https
        var username = info.username     // 设备网页登录用户名
        var password = info.password     // 设备网页登录密码

        var url = proto + '://' + ip + ':' + port + '/request.php'
        console.log(info)
        console.log(url)
        var data = {
          type: 'get_live_stream_type',
          module: 'BUS_WEB_REQUEST'
        }
        axios({
          method: 'POST',
          url: url,
          data: JSON.stringify(data),
          timeout: 10 * 1000,
          withCredentials: false
        }).then(function(res) {
          let ajaxdata = res.data
          if (ajaxdata.state !== 200) {
            _this.$message({
              duration: 2000,
              type: 'error',
              message: '参数错误，请调整！'
            })
          } else {
            _this.video_success = true
            var video_port = ajaxdata.body.port
            var pubkey = decode(ajaxdata.body.pubkey)
            let encryptStr = new JSEncrypt()
            encryptStr.setPublicKey(pubkey)
            var str = username + ':' + password
            var token = encodeURIComponent(encryptStr.encrypt(str.toString()))

            var video_url = 'ws://' + ip + ':' + video_port + '/ws.flv?token=' + token
            _this.init_video('video' + video_index, video_url)
          }
        })
      },
      clickPlateNumber() {
        this.listQuery.filter.plate_number = ''
      },
      simulateIn(plate) {
        let _this = this
        let url = 'http://**************:9191/device/push'
        var tmp = Date.parse(new Date()).toString()
        let sn = plate.sn
        let timestamp = plate.now ? parseInt(tmp.substr(0, 10)) : parseInt(tmp.substr(0, 10)) - parseInt(plate.timestmp)
        localStorage.plate = plate.plate
        // let timestamp = parseInt(plate.timestmp)
        // let timestamp = **********
        let license = plate.plate
        let data = {
          'AlarmInfoPlate': {
            'channel': 0,
            'deviceName': 'South_In',
            'ipaddr': '**************',
            'result': {
              'PlateResult': {
                'bright': 0,
                'carBright': 0,
                'carColor': 255,
                'colorType': 1,
                'colorValue': 0,
                'confidence': 99,
                'direction': 4,
                'gioouts': [],
                'isoffline': 0,
                'license': license,
                'location': {
                  'RECT': {
                    'bottom': 820,
                    'left': 1066,
                    'right': 1400,
                    'top': 651
                  }
                },
                'plateid': 35,
                'timeStamp': {
                  'Timeval': {
                    'decday': 27,
                    'dechour': 12,
                    'decmin': 21,
                    'decmon': 10,
                    'decsec': 5,
                    'decyear': 2021,
                    'sec': timestamp,
                    'usec': 415039
                  }
                },
                'timeUsed': 0,
                'triggerType': 8,
                'type': 1
              }
            },
            'serialno': sn,
            'user_data': ''
          }
        }
        simulatePlate(data).then(response => {
          console.log(response)
          // _this.enter_list = response['items']
          // _this.total = response['totalCount']
          // _this.listLoading = false
        })
      },
      init_video(v_id, url) {
        console.log('video url', url)
        let container = document.getElementById(v_id)
        let player = new window.Jessibuca({ container: container, videoBuffer: 0.2, isResize: false, forceNoOffscreen: true, isNotMute: false, timeout: 10 })
        player.on('load', function() {
          player.play(url)
        })
        player.on('error', function() {
          this.$message({
            duration: 2000,
            type: 'error',
            message: '播放错误'
          })
        })
        player.on('timeout', function() {
          this.$message({
            duration: 2000,
            type: 'error',
            message: '播放超时'
          })
        })
      },
      //获取记录停车数据
      getEnterRecordList() {
        let _this = this
        _this.listLoading = true
        let para = Object.assign({}, _this.listQuery2)
        para.direction = 'in'
        _this.gate ? para.in_gate_id = _this.gate.id : ''
        para = Object.assign({}, para)
        getParkRecord(para).then(response => {
          _this.enter_list = response['items']
          _this.total = response['totalCount']
          _this.listLoading = false
        })
      },
      getExitRecordList() {
        let _this = this
        _this.listLoading = true
        let para = Object.assign({}, _this.listQuery2)
        para.direction = 'out'
        _this.gate ? para.out_gate_id = _this.gate.id : ''
        para = Object.assign({}, para)
        getParkRecord(para).then(response => {
          _this.exit_list = response['items']
          _this.total = response['totalCount']
          _this.listLoading = false
        })
      },
      getNowOutPlate() {
        let _this = this
        _this.listLoading = true
        if (!_this.gate['out_monitor']) {
          return
        }
        getNowOutPlate(_this.gate.id).then(response => {
          if (response.code == 20000) {
            if (response.item && response.out_monitor) {
              _this.out_record = Object.assign(response.item, response.out_monitor)
            } else {
              _this.out_record = response.item
            }
            if (_this.out_record && !_this.out_record.in_time) {
              _this.out_record.in_time = '无入场纪录'
            }

          }
          _this.listLoading = false
        })
      },
      manualOpen() {
        let _this = this
        let para = {
          id: _this.gate.out_monitor.id,
          reason: _this.break_open_form.reason
        }
        console.log('doing manual:', _this.gate.out_monitor.id)
        manualOpenDevice(para).then(res => {
          if (res.code === 20000) {
            _this.breakVisiable = false
            _this.$message({
              type: 'success',
              message: '开闸成功'
            })
          } else {
            _this.$message({
              type: 'error',
              message: '开闸失败'
            })
          }
        })

      },

      videoFormInput() {
        localStorage.form = JSON.stringify(this.form)

      },
      getRTMonitortList() {
        let _this = this
        _this.listLoading = true
        // _this.gate = { in_monitor: {}, out_monitor: {} }
        _this.gate_list = [{ in_monitor: {}, out_monitor: {} }];
        (_this.gate_id ? getGateDetail(_this.gate_id) : getGate()).then(response => {
          // _this.list = response.items;
          if (response.code === 20000) {
            if (_this.gate_id) {
              Vue.set(_this, 'gate', response.items)
              _this.out_monitor = response.items.out_monitor
            } else {
              Vue.set(_this, 'gate_list', response.items)
            }
            _this.$message({
              type: 'success',
              message: '刷新成功'
            })
            _this.listLoading = false
          } else {

            _this.$message({
              type: 'error',
              message: '刷新失败'
            })
          }
        })
        setTimeout(() => {
          _this.listLoading = false
        }, 3.5 * 1000)
      },
      PicUrlAppend(picUrl) {
        let baseURL = process.env.VUE_APP_BASE_API
        return picUrl ? baseURL + picUrl : ''
      },
      checkOnDuty() {
        let _this = this
        let onduty = false
        this.roles.forEach(element => {
          element.name.indexOf('watcher') > -1 ? onduty = true : ''
        })
        if (onduty) {
          _this.gate_list = []
          getGate().then(response => {
            _this.gate_list = response.items
          })
          if (localStorage.gate) {
            _this.gate = JSON.parse(localStorage.gate)
            _this.first_time = localStorage.first_time === 'true'
          }
          console.log(_this.gate)
          if (!_this.gate || _this.first_time) {
            _this.dutyDialogVisiable = true
          } else {
            _this.playAll()
          }
        } else {
          _this.gate = null
        }
        _this.getRTMonitortList()
      },
      setGate() {
        let _this = this
        if (!_this.gate) {
          _this.$message({
            type: 'error',
            message: '请选择岗亭！！！'
          })
          return
        }
        let para = {
          gate_id: _this.gate.id
        }
        setSystemUserDuty(para).then(res => {
          if (res.code === 20000) {
            _this.$message({
              type: 'success',
              message: res.message
            })
            _this.playAll()
            localStorage.gate = JSON.stringify(_this.gate)
            localStorage.first_time = false
            _this.dutyDialogVisiable = false
          } else {
            _this.$message({
              type: 'error',
              message: res.message
            })
          }
        })
      },
      beClose() {
        this.$message({
          type: 'warning',
          message: '请选择岗亭！！！'
        })
      },
      // 格式化表格数据
      tableFormat(row, column, index) {
        // console.log(row, column, index)
        let str = ''
        switch (column.property) {
          case 'update_time':
            str = row.update_time ? row.update_time.slice(0, 10) : ''
            break
          case 'create_time':
            str = row.create_time.slice(0, 10)
            break
          case 'in_gate':
            str = row.in_gate === 0 ? '未出场' : row.in_gate.name
            break
          case 'out_gate':
            str = row.out_gate === 0 ? '未出场' : row.out_gate.name
            break
          case 'out_handler':
            str = row.out_handler == '' ? '系统放行' : row.out_handler
            break
          case 'pay_type':
            str = this.pay_type_list[row.pay_type]
            break
          case 'status':
            str = this.car_status_list[row.status]
            break

          // case 'out_time':
          //   str = row.out_time ? row.out_time.slice(0, 10) : ''
          //   break
        }
        return str
      },
      tableRowClassName({ row, rowIndex }) {
        return 'table-header'
      },
      imgClick(url) {
        window.open(url)
        // window.open('https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg')
      }
    }
  }
</script>
<style>

  .lb {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    justify-content: space-between;
  }

  .lb .el-form-item {
    display: inline-block;
    width: 40%;
    margin-left: 5%;
    margin-right: 5%;
  }

  .lb .el-input--medium .el-input__inner {
    max-width: 400px;
    display: inline-block
  }


  .content-bar {
    /*border-radius: 10px;*/
  }

  .content-title {
    padding-left: 15px;
    /*border-top-left-radius: 10px;*/
    /*border-top-right-radius: 10px;*/
    font-size: 18px;
    font-weight: bold;
    background-color: #409eff;
    color: white;
    height: 50px;
    line-height: 50px;
  }

  .flex-row {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
  }

  .flex-column {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    width: calc(70% - 30px);
    height: 88vh;
  }

  .solid-border {
    /*border: 1px solid #999999;*/
    border-bottom: 1px solid #304156;
    border-left: 1px solid #304156;
    border-right: 1px solid #304156;
    height: 100%;
  }

  .rt-monitor {
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 100%;
    background-color: white;
    height: calc(100% - 50px);
    /*border-bottom-left-radius: 10px;*/
    /*border-bottom-right-radius: 10px;*/
  }

  .rt-video {
    text-align: center;
    /*margin-left: calc((100% - 30vw) / 2);*/
    /*width: 30vw;*/
    /*height: 18vw;*/
    width: 100%;
    height: calc(100% - 50px);
  }

  .should-charge {
    width: 100%;
    font-size: 50px;
    text-align: center;
    margin-bottom: 10%;
  }

  .detail-info {
    width: 100%;
    font-size: 30px;
    margin-bottom: 10%;
  }

  .detail-info .child {
    margin-bottom: 3%;
  }

  .detail-info .label {
    text-align: right;
    width: 45%;
    display: inline-block;
  }

  .detail-info .content {
    text-align: left;
    width: 50%;
    display: inline-block;
  }

  .button {
    width: 40%;
    margin-left: 35%;
    font-size: 30px;
  }

  .content-table {
    /*border-bottom-left-radius: 10px;*/
    /*border-bottom-right-radius: 10px;*/
  }

  .table-header {
    font-size: 15px;
    color: black;
    border-bottom: 2px solid #304156;

  }

  .grey {
    background-color: #304156;
  }

  .blue {
    background-color: #409eff;
  }

  .green {
    background-color: #67c23a;
  }

  body .el-table tr:first-child th {
    font-size: 15px;
    color: black;
    border-bottom: 2px solid #999999;

  }
</style>

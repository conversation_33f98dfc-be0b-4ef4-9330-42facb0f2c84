<template>
  <div class="app-container">
    <div class="filter-container">
      <el-row>

        <el-input
          v-model="listQuery.key_word"
          placeholder="关键字"
          style="width: 200px;"
          class="filter-item"
        />
        <el-button

          class="filter-item"
          type="info"
          size="mini"
          style="width: 60px;"
          @click="getDeviceList"
        >搜索
        </el-button>
        <el-button
          class="filter-item"
          type="primary"
          size="mini"
          icon="el-icon-plus"
          style="float: right"
          @click="addDeviceForm"
        >添加设备
        </el-button>

      </el-row>
    </div>
    <el-table
      empty-text="-"
      v-loading="listLoading"
      class="el-table"
      :data="list"
      fit
      highlight-current-row
      style="width: 100%;">
      <el-table-column type="index" width="50"/>
      <el-table-column v-for="(ctl,index) in table_ctl" :formatter="ctl.formatter" :sortable="ctl.sortable" :key="index" :label="ctl.label" :prop="ctl.prop" v-if="ctl.visiable"
                       align="center"></el-table-column>
      <el-table-column label="设备二维码" align="center">
        <template slot-scope="scope">
          <el-image v-if="scope.row.in_out === 1" :src="parseQRcodeURL(scope.row.qr_address)">

          </el-image>
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200px">
        <template slot="header" slot-scope="scope">
          <span style="margin-right: 15px">操作</span>
          <el-tooltip class="item" effect="dark" :hide-after="500" content="设置列" placement="top">
            <el-button icon="el-icon-setting" @click="table_dialog_form_visiable = true" circle type="text"></el-button>
          </el-tooltip>
        </template>
        <template slot-scope="scope">
          <el-tooltip v-if="scope.row.in_out === 1" class="item" effect="dark" :hide-after="500" content="生成二维码" placement="top">
            <el-button icon="el-icon-refresh" class="my-green" circle size="medium" @click="createQRcode(scope.row)"></el-button>
          </el-tooltip>
          <el-tooltip class="item" effect="dark" :hide-after="500" content="编辑" placement="top">
            <el-button icon="el-icon-edit" class="my-blue" circle size="medium" @click="editDeviceForm(scope.row)"></el-button>
          </el-tooltip>
          <el-tooltip class="item" effect="dark" :hide-after="500" content="删除" placement="top">
            <el-button icon="el-icon-delete" class="my-red" circle size="medium" @click="showDeleteForm(scope.row)"></el-button>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      style="width: 100%;"
      custom-class="dialogwidth">
      <el-form
        ref="dataForm"
        :model="form"
        label-position="right"
        label-width="200px">
        <div class="">
          <el-form-item label="设备名称：">
            <el-input v-model="form.name"></el-input>
          </el-form-item>
          <el-form-item label="序列号：">
            <el-input v-model="form.serial_no"></el-input>
          </el-form-item>
          <el-form-item label="所属出入口：">
            <el-select style="width: 100%;" v-model="form.gate_id">
              <el-option :label="i.name" :value="i.id" v-for="(i,index) in enter_list" :key="index">
              </el-option>
            </el-select>
          </el-form-item>

          <!--          <el-form-item label="接口：">-->
          <!--            <el-input v-model="form.API"></el-input>-->
          <!--          </el-form-item>-->
          <!--          <el-form-item label="命令：">-->
          <!--            <el-input v-model="form.COMMAND"></el-input>-->
          <!--          </el-form-item>-->

          <el-form-item label="设备类型：">
            <el-select style="width: 100%;" v-model="form.device_type">
              <el-option :label="i.label" :value="i.value" v-for="(i,index) in type_list" :key="index">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="出入类型：">
            <el-select style="width: 100%;" v-model="form.in_out">
              <el-option :label="i.label" :value="i.value" v-for="(i,index) in inout_list" :key="index">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="设备本地IP：">
            <el-input v-model="form.ip_address"></el-input>
          </el-form-item>
          <el-form-item label="设备管理用户名：">
            <el-input v-model="form.admin_account"></el-input>
          </el-form-item>
          <el-form-item label="设备管理密码：">
            <el-input v-model="form.admin_password"></el-input>
          </el-form-item>
          <!--          <el-form-item label="运行状态：">-->
          <!--            <el-button icon="el-icon-switch-button" size="medium" @click="form.RUN_STATUS = '0'" v-loading="form.RUN_STATUS=='0'" type="primary"-->
          <!--            >{{form.RUN_STATUS=='1'?'已打开':'已关闭'}}-->
          <!--            </el-button>-->
          <!--          </el-form-item>-->
          <!--          <el-form-item label="操作状态：">-->
          <!--            <el-button icon="el-icon-switch-button" size="medium" @click="form.OP_STATUS = '0'" v-loading="form.OP_STATUS=='0'" type="primary"-->
          <!--            >{{form.OP_STATUS=='1'?'已打开':'已关闭'}}-->
          <!--            </el-button>-->
          <!--          </el-form-item>-->
        </div>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="dialogStatus==='create'?addDevice():editDevice()">确定</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="表格列编辑"
      :visible.sync="table_dialog_form_visiable"
      style="width: 100%;"
      custom-class="dialogwidth">
      <el-form
        ref="dataForm"
        label-position="left"
        label-width="200px">
        <div class="lb">
          <el-form-item :label="ctl.label + '：'" v-for="(ctl,index) in table_ctl" :key="index">
            <el-switch v-model="ctl.visiable" active-text="可见" inactive-text="不可见" active-color="#13ce66" inactive-color="#666666"></el-switch>
          </el-form-item>
        </div>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="table_dialog_form_visiable = false" type='primary'>确定</el-button>
      </div>
    </el-dialog>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.page"
      :page-sizes="[20,50,99]"
      :page-size="99"
      :limit.sync="listQuery.limit"
      @pagination="getDeviceList"
    />
  </div>
</template>

<script>
  import Pagination from '@/components/Pagination/index' // secondary package based on el-pagination
  import { getDevice, getDeviceDetail, addDevice, removeDevice, editDevice, getGate ,createDeviceQRcode} from '@/api/device'
  import Vue from 'vue'

  export default {
    components: { Pagination },
    data() {
      return {

        list: [],
        car_option: ['小汽车', '货车', '大型机动车'],
        total: 0,
        listLoading: false,
        form: {
          'MONITOR': [],
          'BRAKE': []
        },
        type_list: [
          {
            label: '监控',
            value: 0
          }
          // {
          //   label: '道闸',
          //   value: 1
          // }
        ],
        inout_list: [
          {
            label: '出口',
            value: 1
          },
          {
            label: '入口',
            value: 0
          }
        ],
        table_dialog_form_visiable: false,
        table_ctl: [
          {
            label: 'ID',
            prop: 'id',
            sortable: false,
            visiable: true,
            formatter: null
          },
          {
            label: '设备名称',
            prop: 'name',
            sortable: false,
            visiable: true,
            formatter: null
          },
          {
            label: '所属出入口',
            prop: 'gate_id',
            sortable: false,
            visiable: true,
            formatter: this.tableFormat
          },
          {
            label: '出入口类型',
            prop: 'in_out',
            sortable: false,
            visiable: true,
            formatter: this.tableFormat
          },
          {
            label: '运行状态',
            prop: 'status',
            sortable: false,
            visiable: true,
            formatter: this.tableFormat
          },
          {
            label: '操作状态',
            prop: 'switch_on',
            sortable: false,
            visiable: true,
            formatter: this.tableFormat
          },
          {
            label: '更新时间',
            prop: 'update_time',
            sortable: false,
            visiable: true,
            formatter: this.tableFormat
          },
          {
            label: '创建时间',
            prop: 'create_time',
            sortable: false,
            visiable: true,
            formatter: this.tableFormat
          }
        ],
        enter_list: [],
        dialogTimeFormVisible: false,
        dialogFormVisible: false,
        zero: '0',
        textMap: {
          edit: '编辑设备',
          create: '添加设备'
        },
        timeSetList: [30, 120, 180],
        dialogStatus: '',
        listQuery: {
          page: 1,
          limit: 99,
          start_time: null,
          end_time: null
        }
      }
    },
    created() {
      this.getDeviceList()
      this.getEnter()
    },
    mounted() {
    },
    methods: {
      //获取设备列表
      getDeviceList() {
        let _this = this
        _this.listLoading = true
        getDevice(this.listQuery).then(response => {
          _this.list = response.items
          _this.total = response.totalCount
          _this.listLoading = false
        })

        setTimeout(() => {
          _this.listLoading = false
        }, 0.5 * 1000)
      },
      //获取出口
      getEnter() {
        let _this = this
        getGate(_this.listQuery).then(response => {
          _this.enter_list = response.items
        })
      },
      //显示添加dialogue
      addDeviceForm() {
        this.resetForm()
        this.dialogStatus = 'create'
        this.dialogFormVisible = true
      },
      // 添加设备
      addDevice() {
        let _this = this
        let para = Object.assign({}, _this.form)
        addDevice(para).then((res) => {
          if (res.code === 20000) {
            _this.$message({
              type: 'success',
              message: res.message
            })
            _this.dialogFormVisible = false
            _this.getDeviceList()
          }
        })
      },
      // 显示编辑dialogue
      editDeviceForm(row) {
        this.resetForm()
        this.form = row
        this.dialogStatus = 'edit'
        this.dialogFormVisible = true
      },
      // 编辑设备
      editDevice() {
        let _this = this
        let para = Object.assign({}, _this.form)
        delete para.update_time
        editDevice(para).then((res) => {
          if (res.code === 20000) {
            _this.$message({
              type: 'success',
              message: res.message
            })
            _this.dialogFormVisible = false
            _this.getDeviceList()
          }
        })
      },
      //获取二维码地址
      parseQRcodeURL(ur){
        return process.env.VUE_APP_BASE_API + ur
      },
      // 更新二维码
      createQRcode(row) {
        let _this = this
        createDeviceQRcode(row.id).then((res) => {
          if (res.code === 20000) {
            _this.$message({
              type: 'success',
              message: '创建二维码成功',
            })
            _this.getDeviceList()
          }
        })
      },
      //重置表单
      resetForm() {
        this.form = {
          'device_type': 0
        }
      },
      //删除设备
      removeDevice(row) {
        let _this = this
        removeDevice(row.id).then((res) => {
          if (res.code === 20000) {
            _this.$message({
              type: 'success',
              message: '删除成功!'
            })
            _this.getDeviceList()
          }
        })

      },
      // 格式化表格数据
      tableFormat(row, column, index) {
        // console.log(row, column, index)
        let str = ''
        switch (column.property) {
          case 'gate_id':
            this.enter_list.forEach((ele, index) => {
              if (ele.id === row.gate_id) {
                str = ele.name
              }
            })
            break
          case 'status':
            str = row.status === 0 ? '启用' : '禁用'
            break
          case 'switch_on':
            str = row.switch_on === 0 ? '启动' : '停止'
            break
          case 'in_out':
            str = row.in_out === 0 ? '入口' : '出口'
            break
          case 'update_time':
            str = row.update_time ? row.update_time.slice(0, 10) : ''
            break
          case 'create_time':
            str = row.create_time.slice(0, 10)
            break
        }
        return str
      },
      // 显示删除弹窗
      showDeleteForm(row) {
        this.$confirm('确定删除设备「' + row.name + '」吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.removeDevice(row)
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除',
            duration: 500
          })
        })
      }
    }
  }
</script>

<style>
  body .el-table tr:first-child th {
    font-size: 15px;
    color: black;
    border-bottom: 2px solid #999999;

  }
</style>

<template>
  <div class="app-container">
    <div class="filter-container">
      <el-row>

        <el-input
          v-model="listQuery.key_word"
          placeholder="关键字"
          style="width: 200px;"
          class="filter-item"
        />
        <el-button

          class="filter-item"
          type="info"
          size="mini"
          style="width: 60px;"
          @click=""
        >搜索
        </el-button>
        <el-button
          class="filter-item"
          type="primary"
          size="mini"
          icon="el-icon-plus"
          style="float: right"
          @click="addEnterForm"
        >添加出入口
        </el-button>

      </el-row>
    </div>
            <el-table
              empty-text="-"
              v-loading="listLoading"
      :data="list"
              class="el-table"
      fit
      highlight-current-row
      style="width: 100%;">
      <el-table-column type="index" width="50"/>
      <el-table-column v-for="(ctl,index) in table_ctl" :formatter="ctl.formatter" :sortable="ctl.sortable" :key="index" :label="ctl.label" :prop="ctl.prop" v-if="ctl.visiable" align="center"></el-table-column>

      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200px">
        <template slot="header" slot-scope="scope">
          <span style="margin-right: 15px">操作</span>
          <el-tooltip  class="item" effect="dark" :hide-after="500" content="设置列" placement="top">
            <el-button icon="el-icon-setting" @click="table_dialog_form_visiable = true" circle type="text"></el-button>
          </el-tooltip>
        </template>
        <template slot-scope="scope">
          <el-tooltip class="item" effect="dark" :hide-after="500" content="编辑" placement="top">
            <el-button icon="el-icon-edit" class="my-blue" circle size="medium" @click="editEnterForm(scope.row)" ></el-button>
          </el-tooltip>
          <el-tooltip class="item" effect="dark" :hide-after="500" content="删除" placement="top">
            <el-button icon="el-icon-delete" class="my-red" circle size="medium" @click="showDeleteForm(scope.row)"></el-button>
          </el-tooltip>
        </template>
      </el-table-column>

    </el-table>

    <el-dialog
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      style="width: 100%;"
      custom-class="dialogwidth">
      <el-form
        ref="dataForm"
        :model="form"
        label-position="right"
        label-width="200px">
        <div class="">
          <el-form-item label="出入口名称：">
            <el-input v-model="form.name"></el-input>
          </el-form-item>
<!--          <el-form-item label="启用：">-->
<!--            <el-switch v-model="form.valid"></el-switch>-->
<!--          </el-form-item>-->
          <!--          <el-divider v-if="form.MONITOR.length!==0"><i class="el-icon-video-camera"></i></el-divider>-->
          <!--          <el-form-item v-if="form.MONITOR.length!==0">-->
          <!--            <el-col :span="8" style="text-align: center;"><span>监控名称</span></el-col>-->
          <!--            <el-col :span="8" style="text-align: center;"><span>运行状态</span></el-col>-->
          <!--            <el-col :span="8" style="text-align: center;"><span>开关状态</span></el-col>-->
          <!--          </el-form-item>-->
          <!--          <el-form-item :label="'监控' + (index + 1) + '：'" v-for="(i,index) in form.MONITOR" :key="index">-->
          <!--            <el-col :span="8" style="text-align: center;">-->
          <!--              <span>{{i.NAME}}</span>-->
          <!--            </el-col>-->
          <!--            <el-col :span="8" style="text-align: center;">-->
          <!--              <span>{{i.STATUS=='1'?'正常运行':'已断开'}}</span>-->
          <!--            </el-col>-->
          <!--            <el-col :span="8" style="text-align: center;">-->
          <!--              <span>{{i.RUN_STATUS=='1'?'打开':'关闭'}}</span>-->
          <!--            </el-col>-->
          <!--          </el-form-item>-->
          <!--          <el-divider v-if="form.BRAKE.length!==0"><i class="el-icon-open"></i></el-divider>-->
          <!--          <el-form-item v-if="form.BRAKE.length!==0">-->
          <!--            <el-col :span="8" style="text-align: center;"><span>道闸名称</span></el-col>-->
          <!--            <el-col :span="8" style="text-align: center;"><span>运行状态</span></el-col>-->
          <!--            <el-col :span="8" style="text-align: center;"><span>开关状态</span></el-col>-->
          <!--          </el-form-item>-->
          <!--          <el-form-item :label="'道闸' + (index2 + 1) + '：'" v-for="(j,index2) in form.BRAKE" :key="index2 + 100">-->
          <!--            <el-col :span="8" style="text-align: center;">-->
          <!--              <span>{{j.NAME}}</span>-->
          <!--            </el-col>-->
          <!--            <el-col :span="8" style="text-align: center;">-->
          <!--              <span>{{j.STATUS=='1'?'正常运行':'已断开'}}</span>-->
          <!--            </el-col>-->
          <!--            <el-col :span="8" style="text-align: center;">-->
          <!--              <span>{{j.RUN_STATUS=='1'?'打开':'关闭'}}</span>-->
          <!--            </el-col>-->
          <!--          </el-form-item>-->
        </div>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="dialogStatus==='create'?addEnter():editEnter()">确定</el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="实时监控"
      :visible.sync="realtimeMonitorDialog"
      custom-class="dialogwidth">
      <el-row>
        <el-button icon="el-icon-refresh" type="primary" style="float: right;margin-bottom: 15px">刷新</el-button>
      </el-row>
      <el-form
        ref="dataForm"
        :model="form"
        label-position="right"
        label-width="0px">
        <div class="">
          <el-form-item label="">
            <span @click="imgClick()">
            <el-image style="width: 60%;margin-left: 20%" :src="'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg'">
              <div slot="placeholder" class="image-slot">
                加载中<span class="dot">...</span>
              </div>
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
            </el-image>
            </span>
          </el-form-item>
          <el-form-item label="">
            <span style="width: 60%;margin-left: 20%">{{total}}22</span>
          </el-form-item>
          <el-form-item label="">
            <el-button style="width: 60%;margin-left: 20%" type="primary">人工开闸</el-button>
          </el-form-item>
        </div>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="realtimeMonitorDialog = false">确定</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="表格列编辑"
      :visible.sync="table_dialog_form_visiable"
      style="width: 100%;"
      custom-class="dialogwidth">
      <el-form
        ref="dataForm"
        label-position="left"
        label-width="200px">
        <div class="lb">
          <el-form-item :label="ctl.label + '：'" v-for="(ctl,index) in table_ctl" :key="index">
            <el-switch v-model="ctl.visiable" active-text="可见" inactive-text="不可见" active-color="#13ce66" inactive-color="#666666"></el-switch>
          </el-form-item>
        </div>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="table_dialog_form_visiable = false" type='primary'>确定</el-button>
      </div>
    </el-dialog>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.page"
      :page-sizes="[20,50,99]"
      :page-size="99"
      :limit.sync="listQuery.limit"
      @pagination="getEnterList"
    />
  </div>
</template>

<script>
  import Pagination from '@/components/Pagination/index' // secondary package based on el-pagination
  import Vue from 'vue'
  import { addGate, editGate, getGate, removeGate, getGateDetail } from '@/api/device'

  export default {
    components: { Pagination },
    data() {
      return {

        list: [],
        car_option: ['小汽车', '货车', '大型机动车'],
        total: 0,
        inout_list: [
          {
            label: '出口',
            value: 1
          },
          {
            label: '入口',
            value: 0
          }
        ],
        listLoading: false,
        table_dialog_form_visiable:false,
        table_ctl:[
          {
            label:'ID',
            prop:'id',
            sortable: false,
            visiable:true,
            formatter:null,
          },
          {
            label:'出入口名称',
            prop :'name',
            sortable: false,
            visiable:true,
            formatter:null,
          },
          {
            label:'出口监控',
            prop :'out_monitor',
            sortable: false,
            visiable:true,
            formatter:this.tableFormat,
          },
          {
            label:'入口监控',
            prop :'in_monitor',
            sortable: false,
            visiable:true,
            formatter:this.tableFormat,
          },
          // {
          //   label:'道闸数量',
          //   prop :'break_num',
          //   sortable: false,
          //   visiable:true,
          //   formatter:null,
          // },
          {
            label:'更新时间',
            prop:'update_time',
            sortable: false,
            visiable:true,
            formatter:this.tableFormat,
          },
          {
            label:'创建时间',
            prop:'create_time',
            sortable: false,
            visiable:true,
            formatter:this.tableFormat,
          },
        ],
        form: {
          'MONITOR': [],
          'BRAKE': []
        },
        realtimeMonitorDialog: false,
        breakDialogVisiable:false,
        dialogFormVisible: false,
        zero: '0',
        textMap: {
          edit: '编辑出入口',
          create: '添加出入口'
        },
        timeSetList: [30, 120, 180],
        dialogStatus: '',
        listQuery: {
          page: 1,
          limit: 99,
          start_time: null,
          end_time: null
        }
      }
    },
    created() {
      this.getEnterList()
    },
    mounted() {
    },
    methods: {
      //获取出入口
      getEnterList() {
        let _this = this
        _this.listLoading = true
        getGate(_this.listQuery).then(response => {
          _this.list = response.items
          _this.total = response.totalCount
          _this.listLoading = false
        })

        setTimeout(() => {
          _this.listLoading = false
        }, 0.5 * 1000)
      },
      //显示添加出入口dialogue
      addEnterForm() {
        this.resetForm()
        this.dialogStatus = 'create'
        this.dialogFormVisible = true
      },
      //添加出入口
      addEnter() {
        let _this = this
        let para = Object.assign({}, _this.form)
        addGate(para).then((res) => {
          if (res.code === 20000) {
            _this.$message({
              type: 'success',
              message: res.message
            })
            _this.dialogFormVisible = false
            _this.getEnterList()
          }
        })
      },
      //显示编辑出入口dialogeu
      editEnterForm(row) {
        this.resetForm()
        this.form = Object.assign(row)
        this.dialogStatus = 'edit'
        this.dialogFormVisible = true
      },
      //编辑出入口
      editEnter() {
        let _this = this
        let para = Object.assign({}, _this.form)
        delete para.update_time
        editGate(para).then((res) => {
          if (res.code === 20000) {
            _this.$message({
              type: 'success',
              message: res.message
            })
            _this.dialogFormVisible = false
            _this.getEnterList()
          }
        })
      },
     //重置表单
      resetForm() {
        this.form = {
          valid:true
        }
      },
      //删除出入口
      removeEnter(row) {
        let _this = this
        removeGate(row.id).then((res) => {
          if (res.code === 20000) {
            _this.$message({
              type: 'success',
              message: '删除成功!'
            })
            _this.getEnterList()
          }
        })

      },
      // 格式化表格数据
      tableFormat(row, column, index) {
        let str = ''
        switch (column.property) {
          case 'out_monitor':
            str = row.out_monitor ? row.out_monitor.name : ''
            break
          case 'update_time':
            str = row.update_time?row.update_time.slice(0,10):''
            break
          case 'create_time':
            str = row.create_time.slice(0,10)
            break
          case 'in_monitor':
            str = row.in_monitor ? row.in_monitor.name : ''
            break
        }
        return str
      },
      // 显示删除弹窗
      showDeleteForm(row) {
        this.$confirm('确定删除出入口「' + row.name + '」吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.removeEnter(row)
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除',
            duration:500,
          })
        })
      },
    }
  }
</script>

<style>
  body .el-table tr:first-child th{
    font-size: 15px;
    color: black;
    border-bottom: 2px solid #999999;

  }
</style>

<template>
  <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from './mixins/resize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    },
    year_business_income: {
      type: Number,
      default: 0
    },
    year_monthly_income: {
      type: Number,
      default: 0
    },
    year_temp_income: {
      type: Number,
      default: 0
    },
  },
  data() {
    return {
      chart: null
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons')

      this.chart.setOption({
        color:['#F4B9A4','#ED7D31','#C46627'],
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b} : {c} ({d}%)'
        },
        legend: {
          left: 'center',
          bottom: '10',
          data: ['月租车1', '临时车1', '商户消费1']
        },
        series: [
          {
            name: (new Date()).getFullYear() + '年总收费金额',
            type: 'pie',
            radius: '70%',
            itemStyle: {
              borderColor: '#fff',
              borderWidth: 2,
            },
            label: {
              alignTo: 'edge',
              formatter: '{name|{b}}\n{value|{c} 元({d}%)}',
              minMargin: 5,
              edgeDistance: 10,
              color: 'black',
              lineHeight: 15,
              rich: {
                time: {
                  fontSize: 10,
                  color: '#999'
                }
              }
            },
            data: [
              { value: this.year_monthly_income, name: '职工用餐' },
              { value: this.year_temp_income, name: '临时用餐' },
              { value: this.year_business_income, name: '商户消费' }
            ],
            emphasis: {
              itemStyle: {
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)',
              }
            },
            animationEasing: 'cubicInOut',
            animationDuration: 2600
          }
        ]
      })
    },
    update(data){
      this.chart.setOption({
        series: [{
          data: [
            { value: data.year_monthly_income, name: '月租车' },
            { value: data.year_temp_income, name: '临时车' },
            { value: data.year_business_income, name: '商户消费' }
          ],
        }]
      });
    },
  }
}
</script>

<template>
  <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from './mixins/resize'

const animationDuration = 6000

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    },
    monthly_counts: {
      type: Array,
      default: function() {
        return [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
      }
    },
    temp_counts: {
      type: Array,
      default: function() {
        return [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
      }
    },
    total_incomes: {
      type: Array,
      default: function() {
        return [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
      }
    },
  },
  data() {
    return {
      chart: null
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons')

      this.chart.setOption({
        color:['#38B3C3','#ED7D31'],
        tooltip: {
          trigger: 'axis',
          axisPointer: { // 坐标轴指示器，坐标轴触发有效
            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
          }
        },
        grid: {
          top: '2%',
          left: '10%',
          right: '10%',
          bottom: '2%',
          containLabel: true
        },
        legend: {
          data: ['职工用餐次数', '临时用餐次数', '总收费金额']
        },
        xAxis: [{
          type: 'category',
          data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
          axisTick: {
            alignWithLabel: true
          }
        }],
        yAxis: [{
          type: 'value',
          axisTick: {
            show: false
          }
        }],
        series: [{
          name: '职工用餐次数',
          type: 'bar',
          stack: 'vistors',
          data: this.monthly_counts,
          label: {
            show: true,
            position: 'inside'
          },
          animationDuration
        }, {
          name: '临时用餐次数',
          type: 'bar',
          stack: 'vistors',
          label: {
            show: true,
            position: 'inside'
          },
          data: this.temp_counts,
          animationDuration
        },
        {
          name: '总收费金额',
          type: 'line',
          label: {
            show: true,
            color: 'black',
          },
          data: this.total_incomes,
          smooth: false,
          lineStyle: {
            type: 'dashed',
            width: 3,
            color: '#696969'
          },
          animationDuration
        }

        ]
      })
    },
    update(data){
      this.chart.setOption({
        series: [
          {data: data.monthly_counts},
          {data: data.temp_counts},
          {data: data.total_incomes},
        ]
      });
    },
  }
}
</script>

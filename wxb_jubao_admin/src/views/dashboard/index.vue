<template>
  <div>
<!--    <div class="dashboard-editor-container">-->
<!--    <github-corner class="github-corner"/>-->

<!--    <el-row style="height: 30vh;">-->
<!--      <el-col :span="12">-->
<!--        <h1>每日监测</h1>-->
<!--        &lt;!&ndash;        <panel-group @handleSetLineChartData="handleSetLineChartData"/>&ndash;&gt;-->
<!--        <div class="flex-row">-->
<!--          <div style="" class="flex-column">-->
<!--            <div>用餐人数</div>-->
<!--            <div style="" class="divider"></div>-->
<!--            <div>{{chartData.today_records}}</div>-->
<!--          </div>-->
<!--          <div style="" class="flex-column2">-->
<!--            <div>收费金额</div>-->
<!--            <div style="" class="divider"></div>-->
<!--            <div>{{chartData.today_income}}</div>-->
<!--          </div>-->
<!--        </div>-->
<!--      </el-col>-->
<!--      <el-col :span="12">-->
<!--        <h1 class="text-center">{{year}}年总收费金额</h1>-->
<!--        <pie-chart ref="pieChart" :year_business_income="chartData.year_business_income" :year_monthly_income="chartData.year_monthly_income" :year_temp_income="chartData.year_temp_income"/>-->
<!--      </el-col>-->
<!--    </el-row>-->

<!--    <el-row :gutter="32" style="height:  70vh;">-->
<!--      <el-col :span="24">-->
<!--        <h1 class="text-center"><span style="float: left;text-decoration: underline;text-underline: #2d2f33;color: #7d7d7f;">{{year}}年</span>每月入场流量&收费趋势图</h1>-->
<!--        <bar-chart ref="barChart" :total_incomes="chartData.total_incomes" :monthly_counts="chartData.monthly_counts" :temp_counts="chartData.temp_counts"/>-->
<!--      </el-col>-->
<!--    </el-row>-->

    <el-dialog
      title="选择岗亭"
      :visible.sync="dutyDialogVisiable"
      style="width: 100%;"
      :close-on-click-modal=false
      :before-close="beClose"
      custom-class="dialogwidth">
      <el-form
        ref="dataForm"
        label-position="right"
        label-width="200px">
        <el-form-item label="岗亭">
          <el-select style="width: 100%;" v-model="gate" value-key="id">
            <el-option :label="i.name" :value="i" v-for="(i,index) in gate_list" :key="index">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="setGate">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import GithubCorner from '@/components/GithubCorner'
  import PanelGroup from './components/PanelGroup'
  import PieChart from './components/PieChart'
  import BarChart from './components/BarChart'
  import { mapGetters } from 'vuex'
  import { getGate } from '@/api/device'
  import { setSystemUserDuty } from '@/api/system'
  import { getDashboardReport } from '@/api/report'
  import Vue from 'vue'

  const lineChartData = {
    newVisitis: {
      expectedData: [100, 120, 161, 134, 105, 160, 165],
      actualData: [120, 82, 91, 154, 162, 140, 145]
    },
    messages: {
      expectedData: [200, 192, 120, 144, 160, 130, 140],
      actualData: [180, 160, 151, 106, 145, 150, 130]
    },
    purchases: {
      expectedData: [80, 100, 121, 104, 105, 90, 100],
      actualData: [120, 90, 100, 138, 142, 130, 130]
    },
    shoppings: {
      expectedData: [130, 140, 141, 142, 145, 150, 160],
      actualData: [120, 82, 91, 154, 162, 140, 130]
    }
  }

  export default {
    name: 'DashboardAdmin',
    components: {
      GithubCorner,
      PanelGroup,
      PieChart,
      BarChart
    },
    data() {
      return {
        lineChartData: lineChartData.newVisitis,
        dutyDialogVisiable: false,
        gate_list: [],
        gate: null,
        year:(new Date()).getFullYear(),
        first_time: true,
        chartData:{
          monthly_counts: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
          temp_counts: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
          today_income: "0",
          today_records: "0",
          total_incomes: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
          year_business_income: 0,
          year_monthly_income: 0,
          year_temp_income: 0,
        },
      }
    },
    computed: {
      ...mapGetters([
        'roles'
      ])
    },
    methods: {
      handleSetLineChartData(type) {
        this.lineChartData = lineChartData[type]
      },
      //更新饼图
      updatepieChart(){
        let _this = this
        setTimeout(() => {
          _this.$refs.pieChart.update(_this.chartData);
        }, 15);
      },
      //更新条形图
      updatebarChart(){
        let _this = this
        setTimeout(() => {
          _this.$refs.barChart.update(_this.chartData);
        }, 15);
      },
      //警告
      beClose() {
        this.$message({
          type: 'warning',
          message: '请选择岗亭！！！'
        })
      },
      //获取报表
      getReport() {
        let _this = this
        getDashboardReport().then(response => {
          Vue.set(_this,'chartData',response)
          _this.updatepieChart()
          _this.updatebarChart()
        })
      },
      //检查是否值班人员
      checkOnDuty() {
        let _this = this
        let onduty = false
        this.roles.forEach(element => {
          element.name.indexOf('watcher') > -1 ? onduty = true : ''
        })
        if (onduty) {
          _this.gate_list = []
          getGate().then(response => {
            // _this.gate_list = response.items
            Vue.set(_this,'gate_list',response.items)
          })
          if (localStorage.gate) {
            _this.gate = JSON.parse(localStorage.gate)
            _this.first_time = localStorage.first_time === 'true'
          }
          if (!_this.gate || _this.first_time) {
            _this.dutyDialogVisiable = true
          }

        }
      },
      //选择出口
      seleceGate(evt){
        // Vue.set(this,'gate',evt)
        console.log(evt)
      },
      //设置所属出口
      setGate() {
        let _this = this
        if (!_this.gate) {
          _this.$message({
            type: 'error',
            message: '请选择岗亭！！！'
          })
          return
        }
        let para = {
          gate_id: _this.gate.id
        }
        setSystemUserDuty(para).then(res => {
          if (res.code === 20000) {
            _this.$message({
              type: 'success',
              message: res.message
            })
            localStorage.gate = JSON.stringify(_this.gate)
            localStorage.first_time = false
            _this.dutyDialogVisiable = false
          } else {
            _this.$message({
              type: 'error',
              message: res.message
            })
          }
        })
      }
    },
    mounted() {
      // this.checkOnDuty()
      // this.getReport()
    }
  }
</script>

<style lang="scss" scoped>
  .flex-row {
    display: flex;
    flex-direction: row;
    justify-content: center;
  }

  .flex-column {
    width: 25%;
    height: 15vh;
    background: #38B3C3;
    margin: 5vh 20px 0 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    text-align: center;
    font-size: 30px;
    font-weight: bold;
    color: white;
  }

  .flex-column2 {
    width: 25%;
    height: 15vh;
    background: #ED7D31;
    margin: 5vh 0 0 20px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    text-align: center;
    font-size: 30px;
    font-weight: bold;
    color: white;

  }

  .divider {
    width: 60%;
    height: 1px;
    background: white;
    margin: 1vh 20% 1vh 20%;
  }

  .dashboard-editor-container {
    padding: 32px;
    background-color: rgb(240, 242, 245);
    position: relative;

    .github-corner {
      position: absolute;
      top: 0px;
      border: 0;
      right: 0;
    }

    .chart-wrapper {
      background: #fff;
      padding: 16px 16px 0;
      margin-bottom: 32px;
    }
  }

  @media (max-width: 1024px) {
    .chart-wrapper {
      padding: 8px;
    }
  }
</style>

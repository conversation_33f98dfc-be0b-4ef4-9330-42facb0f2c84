# just a flag
ENV = 'development'

# base api
#VUE_APP_BASE_API = 'http://localhost:9191/'
VUE_APP_BASE_API = 'http://localhost:9290/'
#正式服
#VUE_APP_BASE_API = 'http://**************:9290/'



# vue-cli uses the VUE_CLI_BABEL_TRANSPILE_MODULES environment variable,
# to control whether the babel-plugin-dynamic-import-node plugin is enabled.
# It only does one thing by converting all import() to require().
# This configuration can significantly increase the speed of hot updates,
# when you have a large number of pages.
# Detail:  https://github.com/vuejs/vue-cli/blob/dev/packages/@vue/babel-preset-app/rt-monitor.js

VUE_CLI_BABEL_TRANSPILE_MODULES = true
